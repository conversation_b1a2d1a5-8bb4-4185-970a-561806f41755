import { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Plus, Search, Filter } from "lucide-react"

export const metadata: Metadata = {
  title: "Students | QRSAMS",
  description: "Manage student records and information",
}

// Mock student data
const students = [
  {
    id: "STU001",
    name: "<PERSON>",
    grade: "Grade 12",
    section: "A",
    status: "Active",
    lastSeen: "2024-01-08 8:15 AM",
  },
  {
    id: "STU002", 
    name: "<PERSON>",
    grade: "Grade 11",
    section: "B",
    status: "Active",
    lastSeen: "2024-01-08 8:12 AM",
  },
  {
    id: "STU003",
    name: "<PERSON>", 
    grade: "Grade 12",
    section: "A",
    status: "Active",
    lastSeen: "2024-01-08 8:10 AM",
  },
  {
    id: "STU004",
    name: "<PERSON>",
    grade: "Grade 10",
    section: "C",
    status: "Inactive",
    lastSeen: "2024-01-05 3:30 PM",
  },
]

export default function StudentsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Students</h1>
          <p className="text-muted-foreground">
            Manage student records and information
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Student
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Student Directory</CardTitle>
          <CardDescription>
            View and manage all registered students
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search students..." className="pl-8" />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Grade & Section</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Seen</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {students.map((student) => (
                <TableRow key={student.id}>
                  <TableCell className="font-medium">{student.id}</TableCell>
                  <TableCell>{student.name}</TableCell>
                  <TableCell>{student.grade} - {student.section}</TableCell>
                  <TableCell>
                    <Badge 
                      variant={student.status === "Active" ? "secondary" : "outline"}
                    >
                      {student.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {student.lastSeen}
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
