import { Metada<PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Download, FileText, Calendar, Filter } from "lucide-react"

export const metadata: Metadata = {
  title: "Reports | QRSAMS",
  description: "Generate and view attendance reports",
}

const reportTypes = [
  {
    title: "Daily Attendance Report",
    description: "Detailed attendance for a specific date",
    icon: Calendar,
    frequency: "Daily",
    lastGenerated: "Today, 9:00 AM",
  },
  {
    title: "Weekly Summary Report",
    description: "Weekly attendance summary and trends",
    icon: FileText,
    frequency: "Weekly",
    lastGenerated: "Monday, 8:00 AM",
  },
  {
    title: "Monthly Analytics Report",
    description: "Comprehensive monthly attendance analysis",
    icon: FileText,
    frequency: "Monthly",
    lastGenerated: "Jan 1, 2024",
  },
  {
    title: "Student Individual Report",
    description: "Individual student attendance history",
    icon: FileText,
    frequency: "On-demand",
    lastGenerated: "Yesterday, 2:30 PM",
  },
]

const recentReports = [
  {
    name: "Daily Attendance - Jan 8, 2024",
    type: "Daily",
    generatedAt: "Today, 9:00 AM",
    size: "2.3 MB",
    status: "Ready",
  },
  {
    name: "Weekly Summary - Week 1, Jan 2024",
    type: "Weekly", 
    generatedAt: "Monday, 8:00 AM",
    size: "5.7 MB",
    status: "Ready",
  },
  {
    name: "Monthly Report - December 2023",
    type: "Monthly",
    generatedAt: "Jan 1, 2024",
    size: "12.4 MB", 
    status: "Ready",
  },
]

export default function ReportsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
          <p className="text-muted-foreground">
            Generate and download attendance reports
          </p>
        </div>
        <Button>
          <FileText className="mr-2 h-4 w-4" />
          Generate Custom Report
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Report Types</h2>
          <div className="grid gap-4">
            {reportTypes.map((report, index) => (
              <Card key={index}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <report.icon className="h-5 w-5 text-primary" />
                      <CardTitle className="text-base">{report.title}</CardTitle>
                    </div>
                    <Badge variant="outline">{report.frequency}</Badge>
                  </div>
                  <CardDescription>{report.description}</CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Last generated: {report.lastGenerated}
                    </span>
                    <Button size="sm">Generate</Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Recent Reports</h2>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Download Center</CardTitle>
              <CardDescription>
                Access your recently generated reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentReports.map((report, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">{report.name}</p>
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <span>{report.generatedAt}</span>
                        <span>{report.size}</span>
                        <Badge variant="secondary" className="text-xs">
                          {report.status}
                        </Badge>
                      </div>
                    </div>
                    <Button size="sm" variant="outline">
                      <Download className="mr-2 h-4 w-4" />
                      Download
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
              <CardDescription>
                Report generation statistics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">Reports this month</span>
                  <span className="text-sm font-medium">24</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Total downloads</span>
                  <span className="text-sm font-medium">156</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Storage used</span>
                  <span className="text-sm font-medium">89.2 MB</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
