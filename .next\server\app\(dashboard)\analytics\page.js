(()=>{var a={};a.id=817,a.ids=[817],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10475:(a,b,c)=>{Promise.resolve().then(c.bind(c,26269))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16051:(a,b,c)=>{Promise.resolve().then(c.bind(c,55916))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26269:(a,b,c)=>{"use strict";c.d(b,{Tabs:()=>D,TabsContent:()=>G,TabsList:()=>E,TabsTrigger:()=>F});var d=c(60687),e=c(43210),f=c(70569),g=c(11273),h=c(72942),i=c(46059),j=c(14163),k=c(43),l=c(65551),m=c(96963),n="Tabs",[o,p]=(0,g.A)(n,[h.RG]),q=(0,h.RG)(),[r,s]=o(n),t=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,onValueChange:f,defaultValue:g,orientation:h="horizontal",dir:i,activationMode:o="automatic",...p}=a,q=(0,k.jH)(i),[s,t]=(0,l.i)({prop:e,onChange:f,defaultProp:g??"",caller:n});return(0,d.jsx)(r,{scope:c,baseId:(0,m.B)(),value:s,onValueChange:t,orientation:h,dir:q,activationMode:o,children:(0,d.jsx)(j.sG.div,{dir:q,"data-orientation":h,...p,ref:b})})});t.displayName=n;var u="TabsList",v=e.forwardRef((a,b)=>{let{__scopeTabs:c,loop:e=!0,...f}=a,g=s(u,c),i=q(c);return(0,d.jsx)(h.bL,{asChild:!0,...i,orientation:g.orientation,dir:g.dir,loop:e,children:(0,d.jsx)(j.sG.div,{role:"tablist","aria-orientation":g.orientation,...f,ref:b})})});v.displayName=u;var w="TabsTrigger",x=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,disabled:g=!1,...i}=a,k=s(w,c),l=q(c),m=A(k.baseId,e),n=B(k.baseId,e),o=e===k.value;return(0,d.jsx)(h.q7,{asChild:!0,...l,focusable:!g,active:o,children:(0,d.jsx)(j.sG.button,{type:"button",role:"tab","aria-selected":o,"aria-controls":n,"data-state":o?"active":"inactive","data-disabled":g?"":void 0,disabled:g,id:m,...i,ref:b,onMouseDown:(0,f.m)(a.onMouseDown,a=>{g||0!==a.button||!1!==a.ctrlKey?a.preventDefault():k.onValueChange(e)}),onKeyDown:(0,f.m)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&k.onValueChange(e)}),onFocus:(0,f.m)(a.onFocus,()=>{let a="manual"!==k.activationMode;o||g||!a||k.onValueChange(e)})})})});x.displayName=w;var y="TabsContent",z=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:f,forceMount:g,children:h,...k}=a,l=s(y,c),m=A(l.baseId,f),n=B(l.baseId,f),o=f===l.value,p=e.useRef(o);return e.useEffect(()=>{let a=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,d.jsx)(i.C,{present:g||o,children:({present:c})=>(0,d.jsx)(j.sG.div,{"data-state":o?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":m,hidden:!c,id:n,tabIndex:0,...k,ref:b,style:{...a.style,animationDuration:p.current?"0s":void 0},children:c&&h})})});function A(a,b){return`${a}-trigger-${b}`}function B(a,b){return`${a}-content-${b}`}z.displayName=y;var C=c(96241);function D({className:a,...b}){return(0,d.jsx)(t,{"data-slot":"tabs",className:(0,C.cn)("flex flex-col gap-2",a),...b})}function E({className:a,...b}){return(0,d.jsx)(v,{"data-slot":"tabs-list",className:(0,C.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...b})}function F({className:a,...b}){return(0,d.jsx)(x,{"data-slot":"tabs-trigger",className:(0,C.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...b})}function G({className:a,...b}){return(0,d.jsx)(z,{"data-slot":"tabs-content",className:(0,C.cn)("flex-1 outline-none",a),...b})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},40918:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41382:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},51979:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m,metadata:()=>l});var d=c(37413),e=c(51358),f=c(99455),g=c(55916),h=c(83799),i=c(40918);let j=(0,c(26373).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);var k=c(41382);let l={title:"Analytics | QRSAMS",description:"Attendance analytics and insights"};function m(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Analytics"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Detailed insights and trends for student attendance"})]}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(e.ZB,{className:"text-sm font-medium",children:"Average Attendance"}),(0,d.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(e.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"94.2%"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"+2.1% from last month"})]})]}),(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(e.ZB,{className:"text-sm font-medium",children:"Peak Attendance Day"}),(0,d.jsx)(i.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(e.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"Monday"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"96.8% average rate"})]})]}),(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(e.ZB,{className:"text-sm font-medium",children:"Chronic Absentees"}),(0,d.jsx)(j,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(e.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"12"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Students below 80%"})]})]}),(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(e.ZB,{className:"text-sm font-medium",children:"Perfect Attendance"}),(0,d.jsx)(k.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(e.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"89"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Students with 100%"})]})]})]}),(0,d.jsxs)(g.Tabs,{defaultValue:"overview",className:"space-y-4",children:[(0,d.jsxs)(g.TabsList,{children:[(0,d.jsx)(g.TabsTrigger,{value:"overview",children:"Overview"}),(0,d.jsx)(g.TabsTrigger,{value:"trends",children:"Trends"}),(0,d.jsx)(g.TabsTrigger,{value:"grades",children:"By Grade"}),(0,d.jsx)(g.TabsTrigger,{value:"time",children:"Time Analysis"})]}),(0,d.jsx)(g.TabsContent,{value:"overview",className:"space-y-4",children:(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{children:[(0,d.jsx)(e.ZB,{children:"Attendance Distribution"}),(0,d.jsx)(e.BT,{children:"Current month attendance breakdown"})]}),(0,d.jsx)(e.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,d.jsx)("span",{className:"text-sm",children:"Excellent (95-100%)"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"892 students"}),(0,d.jsx)(f.E,{variant:"secondary",children:"72.3%"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,d.jsx)("span",{className:"text-sm",children:"Good (85-94%)"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"234 students"}),(0,d.jsx)(f.E,{variant:"secondary",children:"19.0%"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),(0,d.jsx)("span",{className:"text-sm",children:"Fair (75-84%)"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"96 students"}),(0,d.jsx)(f.E,{variant:"secondary",children:"7.8%"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full"}),(0,d.jsx)("span",{className:"text-sm",children:"Poor (Below 75%)"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"12 students"}),(0,d.jsx)(f.E,{variant:"destructive",children:"0.9%"})]})]})]})})]}),(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{children:[(0,d.jsx)(e.ZB,{children:"Weekly Trends"}),(0,d.jsx)(e.BT,{children:"Attendance patterns by day of week"})]}),(0,d.jsx)(e.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:[{day:"Monday",rate:96.8,trend:"up"},{day:"Tuesday",rate:95.2,trend:"up"},{day:"Wednesday",rate:94.1,trend:"down"},{day:"Thursday",rate:93.7,trend:"down"},{day:"Friday",rate:91.5,trend:"down"}].map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:a.day}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)("span",{className:"text-sm",children:[a.rate,"%"]}),"up"===a.trend?(0,d.jsx)(h.A,{className:"h-4 w-4 text-green-500"}):(0,d.jsx)(j,{className:"h-4 w-4 text-red-500"})]})]},b))})})]})]})}),(0,d.jsx)(g.TabsContent,{value:"trends",children:(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{children:[(0,d.jsx)(e.ZB,{children:"Attendance Trends"}),(0,d.jsx)(e.BT,{children:"Historical attendance data and patterns"})]}),(0,d.jsx)(e.Wu,{children:(0,d.jsx)("p",{className:"text-muted-foreground",children:"Trend analysis charts will be displayed here."})})]})}),(0,d.jsx)(g.TabsContent,{value:"grades",children:(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{children:[(0,d.jsx)(e.ZB,{children:"Grade Level Analysis"}),(0,d.jsx)(e.BT,{children:"Attendance breakdown by grade level"})]}),(0,d.jsx)(e.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:[{grade:"Grade 10",students:312,attendance:95.2},{grade:"Grade 11",students:298,attendance:94.8},{grade:"Grade 12",students:624,attendance:93.1}].map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium",children:a.grade}),(0,d.jsxs)("p",{className:"text-sm text-muted-foreground",children:[a.students," students"]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("p",{className:"font-medium",children:[a.attendance,"%"]}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Average"})]})]},b))})})]})}),(0,d.jsx)(g.TabsContent,{value:"time",children:(0,d.jsxs)(e.Zp,{children:[(0,d.jsxs)(e.aR,{children:[(0,d.jsx)(e.ZB,{children:"Time-based Analysis"}),(0,d.jsx)(e.BT,{children:"Attendance patterns by time periods"})]}),(0,d.jsx)(e.Wu,{children:(0,d.jsx)("p",{className:"text-muted-foreground",children:"Time-based analysis will be displayed here."})})]})})]})]})}},55511:a=>{"use strict";a.exports=require("crypto")},55916:(a,b,c)=>{"use strict";c.d(b,{Tabs:()=>e,TabsContent:()=>h,TabsList:()=>f,TabsTrigger:()=>g});var d=c(61369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tabs.tsx","Tabs"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call TabsList() from the server but TabsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tabs.tsx","TabsList"),g=(0,d.registerClientReference)(function(){throw Error("Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tabs.tsx","TabsTrigger"),h=(0,d.registerClientReference)(function(){throw Error("Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tabs.tsx","TabsContent")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83799:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93624:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,51979)),"C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\analytics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,71934)),"C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Desktop\\qrsams\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\analytics\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/analytics/page",pathname:"/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/analytics/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,410,190,39,69,609,144,483,676],()=>b(b.s=93624));module.exports=c})();