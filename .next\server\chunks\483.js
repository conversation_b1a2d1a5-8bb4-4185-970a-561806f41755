"use strict";exports.id=483,exports.ids=[483],exports.modules={43:(a,b,c)=>{c.d(b,{jH:()=>f});var d=c(43210);c(60687);var e=d.createContext(void 0);function f(a){let b=d.useContext(e);return a||b||"ltr"}},363:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("moon",[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]])},1359:(a,b,c)=>{c.d(b,{Oh:()=>f});var d=c(43210),e=0;function f(){d.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??g()),document.body.insertAdjacentElement("beforeend",a[1]??g()),e++,()=>{1===e&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),e--}},[])}function g(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}},7430:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("school",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 3.447 1.724a1 1 0 0 1 .553.894V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-7.382a1 1 0 0 1 .553-.894L6 10",key:"1xqip1"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 7.106-3.553a2 2 0 0 1 1.788 0L20 6",key:"9d2mlk"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]])},9510:(a,b,c)=>{c.d(b,{N:()=>i});var d=c(43210),e=c(11273),f=c(98599),g=c(8730),h=c(60687);function i(a){let b=a+"CollectionProvider",[c,i]=(0,e.A)(b),[j,k]=c(b,{collectionRef:{current:null},itemMap:new Map}),l=a=>{let{scope:b,children:c}=a,e=d.useRef(null),f=d.useRef(new Map).current;return(0,h.jsx)(j,{scope:b,itemMap:f,collectionRef:e,children:c})};l.displayName=b;let m=a+"CollectionSlot",n=(0,g.TL)(m),o=d.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=k(m,c),g=(0,f.s)(b,e.collectionRef);return(0,h.jsx)(n,{ref:g,children:d})});o.displayName=m;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,g.TL)(p),s=d.forwardRef((a,b)=>{let{scope:c,children:e,...g}=a,i=d.useRef(null),j=(0,f.s)(b,i),l=k(p,c);return d.useEffect(()=>(l.itemMap.set(i,{ref:i,...g}),()=>void l.itemMap.delete(i))),(0,h.jsx)(r,{...{[q]:""},ref:j,children:e})});return s.displayName=p,[{Provider:l,Slot:o,ItemSlot:s},function(b){let c=k(a+"CollectionConsumer",b);return d.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},i]}var j=new WeakMap;function k(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=l(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function l(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],j.set(this,!0)}set(a,b){return j.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=l(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],m=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{m||k[a-1]!==b||(m=!0);let c=k[m?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=k(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=k(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return k(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}})},10022:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11096:(a,b,c)=>{c.d(b,{H4:()=>w,bL:()=>v});var d=c(43210),e=c(11273),f=c(13495),g=c(66156),h=c(14163),i=c(57379);function j(){return()=>{}}var k=c(60687),l="Avatar",[m,n]=(0,e.A)(l),[o,p]=m(l),q=d.forwardRef((a,b)=>{let{__scopeAvatar:c,...e}=a,[f,g]=d.useState("idle");return(0,k.jsx)(o,{scope:c,imageLoadingStatus:f,onImageLoadingStatusChange:g,children:(0,k.jsx)(h.sG.span,{...e,ref:b})})});q.displayName=l;var r="AvatarImage";d.forwardRef((a,b)=>{let{__scopeAvatar:c,src:e,onLoadingStatusChange:l=()=>{},...m}=a,n=p(r,c),o=function(a,{referrerPolicy:b,crossOrigin:c}){let e=(0,i.useSyncExternalStore)(j,()=>!0,()=>!1),f=d.useRef(null),h=e?(f.current||(f.current=new window.Image),f.current):null,[k,l]=d.useState(()=>u(h,a));return(0,g.N)(()=>{l(u(h,a))},[h,a]),(0,g.N)(()=>{let a=a=>()=>{l(a)};if(!h)return;let d=a("loaded"),e=a("error");return h.addEventListener("load",d),h.addEventListener("error",e),b&&(h.referrerPolicy=b),"string"==typeof c&&(h.crossOrigin=c),()=>{h.removeEventListener("load",d),h.removeEventListener("error",e)}},[h,c,b]),k}(e,m),q=(0,f.c)(a=>{l(a),n.onImageLoadingStatusChange(a)});return(0,g.N)(()=>{"idle"!==o&&q(o)},[o,q]),"loaded"===o?(0,k.jsx)(h.sG.img,{...m,ref:b,src:e}):null}).displayName=r;var s="AvatarFallback",t=d.forwardRef((a,b)=>{let{__scopeAvatar:c,delayMs:e,...f}=a,g=p(s,c),[i,j]=d.useState(void 0===e);return d.useEffect(()=>{if(void 0!==e){let a=window.setTimeout(()=>j(!0),e);return()=>window.clearTimeout(a)}},[e]),i&&"loaded"!==g.imageLoadingStatus?(0,k.jsx)(h.sG.span,{...f,ref:b}):null});function u(a,b){return a?b?(a.src!==b&&(a.src=b),a.complete&&a.naturalWidth>0?"loaded":"loading"):"error":"idle"}t.displayName=s;var v=q,w=t},11273:(a,b,c)=>{c.d(b,{A:()=>g,q:()=>f});var d=c(43210),e=c(60687);function f(a,b){let c=d.createContext(b),f=a=>{let{children:b,...f}=a,g=d.useMemo(()=>f,Object.values(f));return(0,e.jsx)(c.Provider,{value:g,children:b})};return f.displayName=a+"Provider",[f,function(e){let f=d.useContext(c);if(f)return f;if(void 0!==b)return b;throw Error(`\`${e}\` must be used within \`${a}\``)}]}function g(a,b=[]){let c=[],f=()=>{let b=c.map(a=>d.createContext(a));return function(c){let e=c?.[a]||b;return d.useMemo(()=>({[`__scope${a}`]:{...c,[a]:e}}),[c,e])}};return f.scopeName=a,[function(b,f){let g=d.createContext(f),h=c.length;c=[...c,f];let i=b=>{let{scope:c,children:f,...i}=b,j=c?.[a]?.[h]||g,k=d.useMemo(()=>i,Object.values(i));return(0,e.jsx)(j.Provider,{value:k,children:f})};return i.displayName=b+"Provider",[i,function(c,e){let i=e?.[a]?.[h]||g,j=d.useContext(i);if(j)return j;if(void 0!==f)return f;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return d.useMemo(()=>({[`__scope${b.scopeName}`]:e}),[e])}};return c.scopeName=b.scopeName,c}(f,...b)]}},11860:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12941:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},13495:(a,b,c)=>{c.d(b,{c:()=>e});var d=c(43210);function e(a){let b=d.useRef(a);return d.useEffect(()=>{b.current=a}),d.useMemo(()=>(...a)=>b.current?.(...a),[])}},16103:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("qr-code",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},21134:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},24772:(a,b,c)=>{c.d(b,{OK:()=>W,bL:()=>U,VM:()=>w,lr:()=>H,LM:()=>V});var d=c(43210),e=c(14163),f=c(46059),g=c(11273),h=c(98599),i=c(13495),j=c(43),k=c(66156),l=c(70569),m=c(60687),n="ScrollArea",[o,p]=(0,g.A)(n),[q,r]=o(n),s=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,type:f="hover",dir:g,scrollHideDelay:i=600,...k}=a,[l,n]=d.useState(null),[o,p]=d.useState(null),[r,s]=d.useState(null),[t,u]=d.useState(null),[v,w]=d.useState(null),[x,y]=d.useState(0),[z,A]=d.useState(0),[B,C]=d.useState(!1),[D,E]=d.useState(!1),F=(0,h.s)(b,a=>n(a)),G=(0,j.jH)(g);return(0,m.jsx)(q,{scope:c,type:f,dir:G,scrollHideDelay:i,scrollArea:l,viewport:o,onViewportChange:p,content:r,onContentChange:s,scrollbarX:t,onScrollbarXChange:u,scrollbarXEnabled:B,onScrollbarXEnabledChange:C,scrollbarY:v,onScrollbarYChange:w,scrollbarYEnabled:D,onScrollbarYEnabledChange:E,onCornerWidthChange:y,onCornerHeightChange:A,children:(0,m.jsx)(e.sG.div,{dir:G,...k,ref:F,style:{position:"relative","--radix-scroll-area-corner-width":x+"px","--radix-scroll-area-corner-height":z+"px",...a.style}})})});s.displayName=n;var t="ScrollAreaViewport",u=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,children:f,nonce:g,...i}=a,j=r(t,c),k=d.useRef(null),l=(0,h.s)(b,k,j.onViewportChange);return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:g}),(0,m.jsx)(e.sG.div,{"data-radix-scroll-area-viewport":"",...i,ref:l,style:{overflowX:j.scrollbarXEnabled?"scroll":"hidden",overflowY:j.scrollbarYEnabled?"scroll":"hidden",...a.style},children:(0,m.jsx)("div",{ref:j.onContentChange,style:{minWidth:"100%",display:"table"},children:f})})]})});u.displayName=t;var v="ScrollAreaScrollbar",w=d.forwardRef((a,b)=>{let{forceMount:c,...e}=a,f=r(v,a.__scopeScrollArea),{onScrollbarXEnabledChange:g,onScrollbarYEnabledChange:h}=f,i="horizontal"===a.orientation;return d.useEffect(()=>(i?g(!0):h(!0),()=>{i?g(!1):h(!1)}),[i,g,h]),"hover"===f.type?(0,m.jsx)(x,{...e,ref:b,forceMount:c}):"scroll"===f.type?(0,m.jsx)(y,{...e,ref:b,forceMount:c}):"auto"===f.type?(0,m.jsx)(z,{...e,ref:b,forceMount:c}):"always"===f.type?(0,m.jsx)(A,{...e,ref:b}):null});w.displayName=v;var x=d.forwardRef((a,b)=>{let{forceMount:c,...e}=a,g=r(v,a.__scopeScrollArea),[h,i]=d.useState(!1);return d.useEffect(()=>{let a=g.scrollArea,b=0;if(a){let c=()=>{window.clearTimeout(b),i(!0)},d=()=>{b=window.setTimeout(()=>i(!1),g.scrollHideDelay)};return a.addEventListener("pointerenter",c),a.addEventListener("pointerleave",d),()=>{window.clearTimeout(b),a.removeEventListener("pointerenter",c),a.removeEventListener("pointerleave",d)}}},[g.scrollArea,g.scrollHideDelay]),(0,m.jsx)(f.C,{present:c||h,children:(0,m.jsx)(z,{"data-state":h?"visible":"hidden",...e,ref:b})})}),y=d.forwardRef((a,b)=>{var c;let{forceMount:e,...g}=a,h=r(v,a.__scopeScrollArea),i="horizontal"===a.orientation,j=S(()=>n("SCROLL_END"),100),[k,n]=(c={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},d.useReducer((a,b)=>c[a][b]??a,"hidden"));return d.useEffect(()=>{if("idle"===k){let a=window.setTimeout(()=>n("HIDE"),h.scrollHideDelay);return()=>window.clearTimeout(a)}},[k,h.scrollHideDelay,n]),d.useEffect(()=>{let a=h.viewport,b=i?"scrollLeft":"scrollTop";if(a){let c=a[b],d=()=>{let d=a[b];c!==d&&(n("SCROLL"),j()),c=d};return a.addEventListener("scroll",d),()=>a.removeEventListener("scroll",d)}},[h.viewport,i,n,j]),(0,m.jsx)(f.C,{present:e||"hidden"!==k,children:(0,m.jsx)(A,{"data-state":"hidden"===k?"hidden":"visible",...g,ref:b,onPointerEnter:(0,l.m)(a.onPointerEnter,()=>n("POINTER_ENTER")),onPointerLeave:(0,l.m)(a.onPointerLeave,()=>n("POINTER_LEAVE"))})})}),z=d.forwardRef((a,b)=>{let c=r(v,a.__scopeScrollArea),{forceMount:e,...g}=a,[h,i]=d.useState(!1),j="horizontal"===a.orientation,k=S(()=>{if(c.viewport){let a=c.viewport.offsetWidth<c.viewport.scrollWidth,b=c.viewport.offsetHeight<c.viewport.scrollHeight;i(j?a:b)}},10);return T(c.viewport,k),T(c.content,k),(0,m.jsx)(f.C,{present:e||h,children:(0,m.jsx)(A,{"data-state":h?"visible":"hidden",...g,ref:b})})}),A=d.forwardRef((a,b)=>{let{orientation:c="vertical",...e}=a,f=r(v,a.__scopeScrollArea),g=d.useRef(null),h=d.useRef(0),[i,j]=d.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),k=N(i.viewport,i.content),l={...e,sizes:i,onSizesChange:j,hasThumb:!!(k>0&&k<1),onThumbChange:a=>g.current=a,onThumbPointerUp:()=>h.current=0,onThumbPointerDown:a=>h.current=a};function n(a,b){return function(a,b,c,d="ltr"){let e=O(c),f=b||e/2,g=c.scrollbar.paddingStart+f,h=c.scrollbar.size-c.scrollbar.paddingEnd-(e-f),i=c.content-c.viewport;return Q([g,h],"ltr"===d?[0,i]:[-1*i,0])(a)}(a,h.current,i,b)}return"horizontal"===c?(0,m.jsx)(B,{...l,ref:b,onThumbPositionChange:()=>{if(f.viewport&&g.current){let a=P(f.viewport.scrollLeft,i,f.dir);g.current.style.transform=`translate3d(${a}px, 0, 0)`}},onWheelScroll:a=>{f.viewport&&(f.viewport.scrollLeft=a)},onDragScroll:a=>{f.viewport&&(f.viewport.scrollLeft=n(a,f.dir))}}):"vertical"===c?(0,m.jsx)(C,{...l,ref:b,onThumbPositionChange:()=>{if(f.viewport&&g.current){let a=P(f.viewport.scrollTop,i);g.current.style.transform=`translate3d(0, ${a}px, 0)`}},onWheelScroll:a=>{f.viewport&&(f.viewport.scrollTop=a)},onDragScroll:a=>{f.viewport&&(f.viewport.scrollTop=n(a))}}):null}),B=d.forwardRef((a,b)=>{let{sizes:c,onSizesChange:e,...f}=a,g=r(v,a.__scopeScrollArea),[i,j]=d.useState(),k=d.useRef(null),l=(0,h.s)(b,k,g.onScrollbarXChange);return d.useEffect(()=>{k.current&&j(getComputedStyle(k.current))},[k]),(0,m.jsx)(F,{"data-orientation":"horizontal",...f,ref:l,sizes:c,style:{bottom:0,left:"rtl"===g.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===g.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":O(c)+"px",...a.style},onThumbPointerDown:b=>a.onThumbPointerDown(b.x),onDragScroll:b=>a.onDragScroll(b.x),onWheelScroll:(b,c)=>{if(g.viewport){let d=g.viewport.scrollLeft+b.deltaX;a.onWheelScroll(d),function(a,b){return a>0&&a<b}(d,c)&&b.preventDefault()}},onResize:()=>{k.current&&g.viewport&&i&&e({content:g.viewport.scrollWidth,viewport:g.viewport.offsetWidth,scrollbar:{size:k.current.clientWidth,paddingStart:M(i.paddingLeft),paddingEnd:M(i.paddingRight)}})}})}),C=d.forwardRef((a,b)=>{let{sizes:c,onSizesChange:e,...f}=a,g=r(v,a.__scopeScrollArea),[i,j]=d.useState(),k=d.useRef(null),l=(0,h.s)(b,k,g.onScrollbarYChange);return d.useEffect(()=>{k.current&&j(getComputedStyle(k.current))},[k]),(0,m.jsx)(F,{"data-orientation":"vertical",...f,ref:l,sizes:c,style:{top:0,right:"ltr"===g.dir?0:void 0,left:"rtl"===g.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":O(c)+"px",...a.style},onThumbPointerDown:b=>a.onThumbPointerDown(b.y),onDragScroll:b=>a.onDragScroll(b.y),onWheelScroll:(b,c)=>{if(g.viewport){let d=g.viewport.scrollTop+b.deltaY;a.onWheelScroll(d),function(a,b){return a>0&&a<b}(d,c)&&b.preventDefault()}},onResize:()=>{k.current&&g.viewport&&i&&e({content:g.viewport.scrollHeight,viewport:g.viewport.offsetHeight,scrollbar:{size:k.current.clientHeight,paddingStart:M(i.paddingTop),paddingEnd:M(i.paddingBottom)}})}})}),[D,E]=o(v),F=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,sizes:f,hasThumb:g,onThumbChange:j,onThumbPointerUp:k,onThumbPointerDown:n,onThumbPositionChange:o,onDragScroll:p,onWheelScroll:q,onResize:s,...t}=a,u=r(v,c),[w,x]=d.useState(null),y=(0,h.s)(b,a=>x(a)),z=d.useRef(null),A=d.useRef(""),B=u.viewport,C=f.content-f.viewport,E=(0,i.c)(q),F=(0,i.c)(o),G=S(s,10);function H(a){z.current&&p({x:a.clientX-z.current.left,y:a.clientY-z.current.top})}return d.useEffect(()=>{let a=a=>{let b=a.target;w?.contains(b)&&E(a,C)};return document.addEventListener("wheel",a,{passive:!1}),()=>document.removeEventListener("wheel",a,{passive:!1})},[B,w,C,E]),d.useEffect(F,[f,F]),T(w,G),T(u.content,G),(0,m.jsx)(D,{scope:c,scrollbar:w,hasThumb:g,onThumbChange:(0,i.c)(j),onThumbPointerUp:(0,i.c)(k),onThumbPositionChange:F,onThumbPointerDown:(0,i.c)(n),children:(0,m.jsx)(e.sG.div,{...t,ref:y,style:{position:"absolute",...t.style},onPointerDown:(0,l.m)(a.onPointerDown,a=>{0===a.button&&(a.target.setPointerCapture(a.pointerId),z.current=w.getBoundingClientRect(),A.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",u.viewport&&(u.viewport.style.scrollBehavior="auto"),H(a))}),onPointerMove:(0,l.m)(a.onPointerMove,H),onPointerUp:(0,l.m)(a.onPointerUp,a=>{let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),document.body.style.webkitUserSelect=A.current,u.viewport&&(u.viewport.style.scrollBehavior=""),z.current=null})})})}),G="ScrollAreaThumb",H=d.forwardRef((a,b)=>{let{forceMount:c,...d}=a,e=E(G,a.__scopeScrollArea);return(0,m.jsx)(f.C,{present:c||e.hasThumb,children:(0,m.jsx)(I,{ref:b,...d})})}),I=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,style:f,...g}=a,i=r(G,c),j=E(G,c),{onThumbPositionChange:k}=j,n=(0,h.s)(b,a=>j.onThumbChange(a)),o=d.useRef(void 0),p=S(()=>{o.current&&(o.current(),o.current=void 0)},100);return d.useEffect(()=>{let a=i.viewport;if(a){let b=()=>{p(),o.current||(o.current=R(a,k),k())};return k(),a.addEventListener("scroll",b),()=>a.removeEventListener("scroll",b)}},[i.viewport,p,k]),(0,m.jsx)(e.sG.div,{"data-state":j.hasThumb?"visible":"hidden",...g,ref:n,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...f},onPointerDownCapture:(0,l.m)(a.onPointerDownCapture,a=>{let b=a.target.getBoundingClientRect(),c=a.clientX-b.left,d=a.clientY-b.top;j.onThumbPointerDown({x:c,y:d})}),onPointerUp:(0,l.m)(a.onPointerUp,j.onThumbPointerUp)})});H.displayName=G;var J="ScrollAreaCorner",K=d.forwardRef((a,b)=>{let c=r(J,a.__scopeScrollArea),d=!!(c.scrollbarX&&c.scrollbarY);return"scroll"!==c.type&&d?(0,m.jsx)(L,{...a,ref:b}):null});K.displayName=J;var L=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,...f}=a,g=r(J,c),[h,i]=d.useState(0),[j,k]=d.useState(0),l=!!(h&&j);return T(g.scrollbarX,()=>{let a=g.scrollbarX?.offsetHeight||0;g.onCornerHeightChange(a),k(a)}),T(g.scrollbarY,()=>{let a=g.scrollbarY?.offsetWidth||0;g.onCornerWidthChange(a),i(a)}),l?(0,m.jsx)(e.sG.div,{...f,ref:b,style:{width:h,height:j,position:"absolute",right:"ltr"===g.dir?0:void 0,left:"rtl"===g.dir?0:void 0,bottom:0,...a.style}}):null});function M(a){return a?parseInt(a,10):0}function N(a,b){let c=a/b;return isNaN(c)?0:c}function O(a){let b=N(a.viewport,a.content),c=a.scrollbar.paddingStart+a.scrollbar.paddingEnd;return Math.max((a.scrollbar.size-c)*b,18)}function P(a,b,c="ltr"){let d=O(b),e=b.scrollbar.paddingStart+b.scrollbar.paddingEnd,f=b.scrollbar.size-e,g=b.content-b.viewport,h=function(a,[b,c]){return Math.min(c,Math.max(b,a))}(a,"ltr"===c?[0,g]:[-1*g,0]);return Q([0,g],[0,f-d])(h)}function Q(a,b){return c=>{if(a[0]===a[1]||b[0]===b[1])return b[0];let d=(b[1]-b[0])/(a[1]-a[0]);return b[0]+d*(c-a[0])}}var R=(a,b=()=>{})=>{let c={left:a.scrollLeft,top:a.scrollTop},d=0;return!function e(){let f={left:a.scrollLeft,top:a.scrollTop},g=c.left!==f.left,h=c.top!==f.top;(g||h)&&b(),c=f,d=window.requestAnimationFrame(e)}(),()=>window.cancelAnimationFrame(d)};function S(a,b){let c=(0,i.c)(a),e=d.useRef(0);return d.useEffect(()=>()=>window.clearTimeout(e.current),[]),d.useCallback(()=>{window.clearTimeout(e.current),e.current=window.setTimeout(c,b)},[c,b])}function T(a,b){let c=(0,i.c)(b);(0,k.N)(()=>{let b=0;if(a){let d=new ResizeObserver(()=>{cancelAnimationFrame(b),b=window.requestAnimationFrame(c)});return d.observe(a),()=>{window.cancelAnimationFrame(b),d.unobserve(a)}}},[a,c])}var U=s,V=u,W=K},25028:(a,b,c)=>{c.d(b,{Z:()=>i});var d=c(43210),e=c(51215),f=c(14163),g=c(66156),h=c(60687),i=d.forwardRef((a,b)=>{let{container:c,...i}=a,[j,k]=d.useState(!1);(0,g.N)(()=>k(!0),[]);let l=c||j&&globalThis?.document?.body;return l?e.createPortal((0,h.jsx)(f.sG.div,{...i,ref:b}),l):null});i.displayName="Portal"},26134:(a,b,c)=>{c.d(b,{UC:()=>$,ZL:()=>Y,bL:()=>X,bm:()=>_,hJ:()=>Z});var d=c(43210),e=c(70569),f=c(98599),g=c(11273),h=c(96963),i=c(65551),j=c(31355),k=c(32547),l=c(25028),m=c(46059),n=c(14163),o=c(1359),p=c(42247),q=c(63376),r=c(8730),s=c(60687),t="Dialog",[u,v]=(0,g.A)(t),[w,x]=u(t),y=a=>{let{__scopeDialog:b,children:c,open:e,defaultOpen:f,onOpenChange:g,modal:j=!0}=a,k=d.useRef(null),l=d.useRef(null),[m,n]=(0,i.i)({prop:e,defaultProp:f??!1,onChange:g,caller:t});return(0,s.jsx)(w,{scope:b,triggerRef:k,contentRef:l,contentId:(0,h.B)(),titleId:(0,h.B)(),descriptionId:(0,h.B)(),open:m,onOpenChange:n,onOpenToggle:d.useCallback(()=>n(a=>!a),[n]),modal:j,children:c})};y.displayName=t;var z="DialogTrigger";d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,g=x(z,c),h=(0,f.s)(b,g.triggerRef);return(0,s.jsx)(n.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":g.open,"aria-controls":g.contentId,"data-state":R(g.open),...d,ref:h,onClick:(0,e.m)(a.onClick,g.onOpenToggle)})}).displayName=z;var A="DialogPortal",[B,C]=u(A,{forceMount:void 0}),D=a=>{let{__scopeDialog:b,forceMount:c,children:e,container:f}=a,g=x(A,b);return(0,s.jsx)(B,{scope:b,forceMount:c,children:d.Children.map(e,a=>(0,s.jsx)(m.C,{present:c||g.open,children:(0,s.jsx)(l.Z,{asChild:!0,container:f,children:a})}))})};D.displayName=A;var E="DialogOverlay",F=d.forwardRef((a,b)=>{let c=C(E,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=x(E,a.__scopeDialog);return f.modal?(0,s.jsx)(m.C,{present:d||f.open,children:(0,s.jsx)(H,{...e,ref:b})}):null});F.displayName=E;var G=(0,r.TL)("DialogOverlay.RemoveScroll"),H=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(E,c);return(0,s.jsx)(p.A,{as:G,allowPinchZoom:!0,shards:[e.contentRef],children:(0,s.jsx)(n.sG.div,{"data-state":R(e.open),...d,ref:b,style:{pointerEvents:"auto",...d.style}})})}),I="DialogContent",J=d.forwardRef((a,b)=>{let c=C(I,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=x(I,a.__scopeDialog);return(0,s.jsx)(m.C,{present:d||f.open,children:f.modal?(0,s.jsx)(K,{...e,ref:b}):(0,s.jsx)(L,{...e,ref:b})})});J.displayName=I;var K=d.forwardRef((a,b)=>{let c=x(I,a.__scopeDialog),g=d.useRef(null),h=(0,f.s)(b,c.contentRef,g);return d.useEffect(()=>{let a=g.current;if(a)return(0,q.Eq)(a)},[]),(0,s.jsx)(M,{...a,ref:h,trapFocus:c.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,e.m)(a.onCloseAutoFocus,a=>{a.preventDefault(),c.triggerRef.current?.focus()}),onPointerDownOutside:(0,e.m)(a.onPointerDownOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey;(2===b.button||c)&&a.preventDefault()}),onFocusOutside:(0,e.m)(a.onFocusOutside,a=>a.preventDefault())})}),L=d.forwardRef((a,b)=>{let c=x(I,a.__scopeDialog),e=d.useRef(!1),f=d.useRef(!1);return(0,s.jsx)(M,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:b=>{a.onCloseAutoFocus?.(b),b.defaultPrevented||(e.current||c.triggerRef.current?.focus(),b.preventDefault()),e.current=!1,f.current=!1},onInteractOutside:b=>{a.onInteractOutside?.(b),b.defaultPrevented||(e.current=!0,"pointerdown"===b.detail.originalEvent.type&&(f.current=!0));let d=b.target;c.triggerRef.current?.contains(d)&&b.preventDefault(),"focusin"===b.detail.originalEvent.type&&f.current&&b.preventDefault()}})}),M=d.forwardRef((a,b)=>{let{__scopeDialog:c,trapFocus:e,onOpenAutoFocus:g,onCloseAutoFocus:h,...i}=a,l=x(I,c),m=d.useRef(null),n=(0,f.s)(b,m);return(0,o.Oh)(),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k.n,{asChild:!0,loop:!0,trapped:e,onMountAutoFocus:g,onUnmountAutoFocus:h,children:(0,s.jsx)(j.qW,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":R(l.open),...i,ref:n,onDismiss:()=>l.onOpenChange(!1)})}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(V,{titleId:l.titleId}),(0,s.jsx)(W,{contentRef:m,descriptionId:l.descriptionId})]})]})}),N="DialogTitle";d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(N,c);return(0,s.jsx)(n.sG.h2,{id:e.titleId,...d,ref:b})}).displayName=N;var O="DialogDescription";d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(O,c);return(0,s.jsx)(n.sG.p,{id:e.descriptionId,...d,ref:b})}).displayName=O;var P="DialogClose",Q=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,f=x(P,c);return(0,s.jsx)(n.sG.button,{type:"button",...d,ref:b,onClick:(0,e.m)(a.onClick,()=>f.onOpenChange(!1))})});function R(a){return a?"open":"closed"}Q.displayName=P;var S="DialogTitleWarning",[T,U]=(0,g.q)(S,{contentName:I,titleName:N,docsSlug:"dialog"}),V=({titleId:a})=>{let b=U(S),c=`\`${b.contentName}\` requires a \`${b.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${b.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${b.docsSlug}`;return d.useEffect(()=>{a&&(document.getElementById(a)||console.error(c))},[c,a]),null},W=({contentRef:a,descriptionId:b})=>{let c=U("DialogDescriptionWarning"),e=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${c.contentName}}.`;return d.useEffect(()=>{let c=a.current?.getAttribute("aria-describedby");b&&c&&(document.getElementById(b)||console.warn(e))},[e,a,b]),null},X=y,Y=D,Z=F,$=J,_=Q},31355:(a,b,c)=>{c.d(b,{qW:()=>m});var d,e=c(43210),f=c(70569),g=c(14163),h=c(98599),i=c(13495),j=c(60687),k="dismissableLayer.update",l=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),m=e.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:m,onPointerDownOutside:p,onFocusOutside:q,onInteractOutside:r,onDismiss:s,...t}=a,u=e.useContext(l),[v,w]=e.useState(null),x=v?.ownerDocument??globalThis?.document,[,y]=e.useState({}),z=(0,h.s)(b,a=>w(a)),A=Array.from(u.layers),[B]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=v?A.indexOf(v):-1,E=u.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let c=(0,i.c)(a),d=e.useRef(!1),f=e.useRef(()=>{});return e.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){o("dismissableLayer.pointerDownOutside",c,e,{discrete:!0})},e={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",f.current),f.current=d,b.addEventListener("click",f.current,{once:!0})):d()}else b.removeEventListener("click",f.current);d.current=!1},e=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(e),b.removeEventListener("pointerdown",a),b.removeEventListener("click",f.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...u.branches].some(a=>a.contains(b));F&&!c&&(p?.(a),r?.(a),a.defaultPrevented||s?.())},x),H=function(a,b=globalThis?.document){let c=(0,i.c)(a),d=e.useRef(!1);return e.useEffect(()=>{let a=a=>{a.target&&!d.current&&o("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...u.branches].some(a=>a.contains(b))&&(q?.(a),r?.(a),a.defaultPrevented||s?.())},x);return!function(a,b=globalThis?.document){let c=(0,i.c)(a);e.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{D===u.layers.size-1&&(m?.(a),!a.defaultPrevented&&s&&(a.preventDefault(),s()))},x),e.useEffect(()=>{if(v)return c&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(d=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(v)),u.layers.add(v),n(),()=>{c&&1===u.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=d)}},[v,x,c,u]),e.useEffect(()=>()=>{v&&(u.layers.delete(v),u.layersWithOutsidePointerEventsDisabled.delete(v),n())},[v,u]),e.useEffect(()=>{let a=()=>y({});return document.addEventListener(k,a),()=>document.removeEventListener(k,a)},[]),(0,j.jsx)(g.sG.div,{...t,ref:z,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:(0,f.m)(a.onFocusCapture,H.onFocusCapture),onBlurCapture:(0,f.m)(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:(0,f.m)(a.onPointerDownCapture,G.onPointerDownCapture)})});function n(){let a=new CustomEvent(k);document.dispatchEvent(a)}function o(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,g.hO)(e,f):e.dispatchEvent(f)}m.displayName="DismissableLayer",e.forwardRef((a,b)=>{let c=e.useContext(l),d=e.useRef(null),f=(0,h.s)(b,d);return e.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,j.jsx)(g.sG.div,{...a,ref:f})}).displayName="DismissableLayerBranch"},32192:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},32547:(a,b,c)=>{c.d(b,{n:()=>l});var d=c(43210),e=c(98599),f=c(14163),g=c(13495),h=c(60687),i="focusScope.autoFocusOnMount",j="focusScope.autoFocusOnUnmount",k={bubbles:!1,cancelable:!0},l=d.forwardRef((a,b)=>{let{loop:c=!1,trapped:l=!1,onMountAutoFocus:q,onUnmountAutoFocus:r,...s}=a,[t,u]=d.useState(null),v=(0,g.c)(q),w=(0,g.c)(r),x=d.useRef(null),y=(0,e.s)(b,a=>u(a)),z=d.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;d.useEffect(()=>{if(l){let a=function(a){if(z.paused||!t)return;let b=a.target;t.contains(b)?x.current=b:o(x.current,{select:!0})},b=function(a){if(z.paused||!t)return;let b=a.relatedTarget;null!==b&&(t.contains(b)||o(x.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&o(t)});return t&&c.observe(t,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[l,t,z.paused]),d.useEffect(()=>{if(t){p.add(z);let a=document.activeElement;if(!t.contains(a)){let b=new CustomEvent(i,k);t.addEventListener(i,v),t.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(o(d,{select:b}),document.activeElement!==c)return}(m(t).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&o(t))}return()=>{t.removeEventListener(i,v),setTimeout(()=>{let b=new CustomEvent(j,k);t.addEventListener(j,w),t.dispatchEvent(b),b.defaultPrevented||o(a??document.body,{select:!0}),t.removeEventListener(j,w),p.remove(z)},0)}}},[t,v,w,z]);let A=d.useCallback(a=>{if(!c&&!l||z.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,d=document.activeElement;if(b&&d){let b=a.currentTarget,[e,f]=function(a){let b=m(a);return[n(b,a),n(b.reverse(),a)]}(b);e&&f?a.shiftKey||d!==f?a.shiftKey&&d===e&&(a.preventDefault(),c&&o(f,{select:!0})):(a.preventDefault(),c&&o(e,{select:!0})):d===b&&a.preventDefault()}},[c,l,z.paused]);return(0,h.jsx)(f.sG.div,{tabIndex:-1,...s,ref:y,onKeyDown:A})});function m(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function n(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function o(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}l.displayName="FocusScope";var p=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=q(a,b)).unshift(b)},remove(b){a=q(a,b),a[0]?.resume()}}}();function q(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}},40083:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},41312:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},42247:(a,b,c)=>{c.d(b,{A:()=>U});var d,e,f=function(){return(f=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function g(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var h=("function"==typeof SuppressedError&&SuppressedError,c(43210)),i="right-scroll-bar-position",j="width-before-scroll-bar";function k(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var l="undefined"!=typeof window?h.useLayoutEffect:h.useEffect,m=new WeakMap;function n(a){return a}var o=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=n),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=f({async:!0,ssr:!1},a),e}(),p=function(){},q=h.forwardRef(function(a,b){var c,d,e,i,j=h.useRef(null),n=h.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),q=n[0],r=n[1],s=a.forwardProps,t=a.children,u=a.className,v=a.removeScrollBar,w=a.enabled,x=a.shards,y=a.sideCar,z=a.noRelative,A=a.noIsolation,B=a.inert,C=a.allowPinchZoom,D=a.as,E=a.gapMode,F=g(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),G=(c=[j,b],d=function(a){return c.forEach(function(b){return k(b,a)})},(e=(0,h.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,i=e.facade,l(function(){var a=m.get(i);if(a){var b=new Set(a),d=new Set(c),e=i.current;b.forEach(function(a){d.has(a)||k(a,null)}),d.forEach(function(a){b.has(a)||k(a,e)})}m.set(i,c)},[c]),i),H=f(f({},F),q);return h.createElement(h.Fragment,null,w&&h.createElement(y,{sideCar:o,removeScrollBar:v,shards:x,noRelative:z,noIsolation:A,inert:B,setCallbacks:r,allowPinchZoom:!!C,lockRef:j,gapMode:E}),s?h.cloneElement(h.Children.only(t),f(f({},H),{ref:G})):h.createElement(void 0===D?"div":D,f({},H,{className:u,ref:G}),t))});q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},q.classNames={fullWidth:j,zeroRight:i};var r=function(a){var b=a.sideCar,c=g(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return h.createElement(d,f({},c))};r.isSideCarExport=!0;var s=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=e||c.nc;return b&&a.setAttribute("nonce",b),a}())){var f,g;(f=b).styleSheet?f.styleSheet.cssText=d:f.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},t=function(){var a=s();return function(b,c){h.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},u=function(){var a=t();return function(b){return a(b.styles,b.dynamic),null}},v={left:0,top:0,right:0,gap:0},w=function(a){return parseInt(a||"",10)||0},x=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[w(c),w(d),w(e)]},y=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return v;var b=x(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},z=u(),A="data-scroll-locked",B=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(i," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(j," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(i," .").concat(i," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(j," .").concat(j," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},C=function(){var a=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(a)?a:0},D=function(){h.useEffect(function(){return document.body.setAttribute(A,(C()+1).toString()),function(){var a=C()-1;a<=0?document.body.removeAttribute(A):document.body.setAttribute(A,a.toString())}},[])},E=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;D();var f=h.useMemo(function(){return y(e)},[e]);return h.createElement(z,{styles:B(f,!b,e,c?"":"!important")})},F=!1;if("undefined"!=typeof window)try{var G=Object.defineProperty({},"passive",{get:function(){return F=!0,!0}});window.addEventListener("test",G,G),window.removeEventListener("test",G,G)}catch(a){F=!1}var H=!!F&&{passive:!1},I=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},J=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),K(a,d)){var e=L(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},K=function(a,b){return"v"===a?I(b,"overflowY"):I(b,"overflowX")},L=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},M=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=L(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&K(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},N=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},O=function(a){return[a.deltaX,a.deltaY]},P=function(a){return a&&"current"in a?a.current:a},Q=0,R=[];let S=(d=function(a){var b=h.useRef([]),c=h.useRef([0,0]),d=h.useRef(),e=h.useState(Q++)[0],f=h.useState(u)[0],g=h.useRef(a);h.useEffect(function(){g.current=a},[a]),h.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(P),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=h.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!g.current.allowPinchZoom;var e,f=N(a),h=c.current,i="deltaX"in a?a.deltaX:h[0]-f[0],j="deltaY"in a?a.deltaY:h[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=J(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=J(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return M(n,b,a,"h"===n?i:j,!0)},[]),j=h.useCallback(function(a){if(R.length&&R[R.length-1]===f){var c="deltaY"in a?O(a):N(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(g.current.shards||[]).map(P).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!g.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=h.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=h.useCallback(function(a){c.current=N(a),d.current=void 0},[]),m=h.useCallback(function(b){k(b.type,O(b),b.target,i(b,a.lockRef.current))},[]),n=h.useCallback(function(b){k(b.type,N(b),b.target,i(b,a.lockRef.current))},[]);h.useEffect(function(){return R.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,H),document.addEventListener("touchmove",j,H),document.addEventListener("touchstart",l,H),function(){R=R.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,H),document.removeEventListener("touchmove",j,H),document.removeEventListener("touchstart",l,H)}},[]);var o=a.removeScrollBar,p=a.inert;return h.createElement(h.Fragment,null,p?h.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?h.createElement(E,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},o.useMedium(d),r);var T=h.forwardRef(function(a,b){return h.createElement(q,f({},a,{ref:b,sideCar:S}))});T.classNames=q.classNames;let U=T},46059:(a,b,c)=>{c.d(b,{C:()=>g});var d=c(43210),e=c(98599),f=c(66156),g=a=>{let{present:b,children:c}=a,g=function(a){var b,c;let[e,g]=d.useState(),i=d.useRef(null),j=d.useRef(a),k=d.useRef("none"),[l,m]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},d.useReducer((a,b)=>c[a][b]??a,b));return d.useEffect(()=>{let a=h(i.current);k.current="mounted"===l?a:"none"},[l]),(0,f.N)(()=>{let b=i.current,c=j.current;if(c!==a){let d=k.current,e=h(b);a?m("MOUNT"):"none"===e||b?.display==="none"?m("UNMOUNT"):c&&d!==e?m("ANIMATION_OUT"):m("UNMOUNT"),j.current=a}},[a,m]),(0,f.N)(()=>{if(e){let a,b=e.ownerDocument.defaultView??window,c=c=>{let d=h(i.current).includes(c.animationName);if(c.target===e&&d&&(m("ANIMATION_END"),!j.current)){let c=e.style.animationFillMode;e.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===e.style.animationFillMode&&(e.style.animationFillMode=c)})}},d=a=>{a.target===e&&(k.current=h(i.current))};return e.addEventListener("animationstart",d),e.addEventListener("animationcancel",c),e.addEventListener("animationend",c),()=>{b.clearTimeout(a),e.removeEventListener("animationstart",d),e.removeEventListener("animationcancel",c),e.removeEventListener("animationend",c)}}m("ANIMATION_END")},[e,m]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:d.useCallback(a=>{i.current=a?getComputedStyle(a):null,g(a)},[])}}(b),i="function"==typeof c?c({present:g.isPresent}):d.Children.only(c),j=(0,e.s)(g.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(i));return"function"==typeof c||g.isPresent?d.cloneElement(i,{ref:j}):null};function h(a){return a?.animationName||"none"}g.displayName="Presence"},53332:(a,b,c)=>{var d=c(43210),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useState,g=d.useEffect,h=d.useLayoutEffect,i=d.useDebugValue;function j(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!e(a,c)}catch(a){return!0}}var k="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,b){return b()}:function(a,b){var c=b(),d=f({inst:{value:c,getSnapshot:b}}),e=d[0].inst,k=d[1];return h(function(){e.value=c,e.getSnapshot=b,j(e)&&k({inst:e})},[a,c,b]),g(function(){return j(e)&&k({inst:e}),a(function(){j(e)&&k({inst:e})})},[a]),i(c),c};b.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:k},53411:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},57124:(a,b,c)=>{c.d(b,{UC:()=>cr,q7:()=>ct,JU:()=>cs,ZL:()=>cq,bL:()=>co,wv:()=>cu,l9:()=>cp});var d=c(43210),e=c(70569),f=c(98599),g=c(11273),h=c(65551),i=c(14163),j=c(9510),k=c(43),l=c(31355),m=c(1359),n=c(32547),o=c(96963);let p=["top","right","bottom","left"],q=Math.min,r=Math.max,s=Math.round,t=Math.floor,u=a=>({x:a,y:a}),v={left:"right",right:"left",bottom:"top",top:"bottom"},w={start:"end",end:"start"};function x(a,b){return"function"==typeof a?a(b):a}function y(a){return a.split("-")[0]}function z(a){return a.split("-")[1]}function A(a){return"x"===a?"y":"x"}function B(a){return"y"===a?"height":"width"}let C=new Set(["top","bottom"]);function D(a){return C.has(y(a))?"y":"x"}function E(a){return a.replace(/start|end/g,a=>w[a])}let F=["left","right"],G=["right","left"],H=["top","bottom"],I=["bottom","top"];function J(a){return a.replace(/left|right|bottom|top/g,a=>v[a])}function K(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function L(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function M(a,b,c){let d,{reference:e,floating:f}=a,g=D(b),h=A(D(b)),i=B(h),j=y(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(z(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let N=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=M(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=M(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function O(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=x(b,a),o=K(n),p=h[m?"floating"===l?"reference":"floating":l],q=L(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=L(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function P(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function Q(a){return p.some(b=>a[b]>=0)}let R=new Set(["left","top"]);async function S(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=y(c),h=z(c),i="y"===D(c),j=R.has(g)?-1:1,k=f&&i?-1:1,l=x(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function T(){return"undefined"!=typeof window}function U(a){return X(a)?(a.nodeName||"").toLowerCase():"#document"}function V(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function W(a){var b;return null==(b=(X(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function X(a){return!!T()&&(a instanceof Node||a instanceof V(a).Node)}function Y(a){return!!T()&&(a instanceof Element||a instanceof V(a).Element)}function Z(a){return!!T()&&(a instanceof HTMLElement||a instanceof V(a).HTMLElement)}function $(a){return!!T()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof V(a).ShadowRoot)}let _=new Set(["inline","contents"]);function aa(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=al(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!_.has(e)}let ab=new Set(["table","td","th"]),ac=[":popover-open",":modal"];function ad(a){return ac.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let ae=["transform","translate","scale","rotate","perspective"],af=["transform","translate","scale","rotate","perspective","filter"],ag=["paint","layout","strict","content"];function ah(a){let b=ai(),c=Y(a)?al(a):a;return ae.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||af.some(a=>(c.willChange||"").includes(a))||ag.some(a=>(c.contain||"").includes(a))}function ai(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let aj=new Set(["html","body","#document"]);function ak(a){return aj.has(U(a))}function al(a){return V(a).getComputedStyle(a)}function am(a){return Y(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function an(a){if("html"===U(a))return a;let b=a.assignedSlot||a.parentNode||$(a)&&a.host||W(a);return $(b)?b.host:b}function ao(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=an(b);return ak(c)?b.ownerDocument?b.ownerDocument.body:b.body:Z(c)&&aa(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=V(e);if(f){let a=ap(g);return b.concat(g,g.visualViewport||[],aa(e)?e:[],a&&c?ao(a):[])}return b.concat(e,ao(e,[],c))}function ap(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function aq(a){let b=al(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=Z(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=s(c)!==f||s(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function ar(a){return Y(a)?a:a.contextElement}function as(a){let b=ar(a);if(!Z(b))return u(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=aq(b),g=(f?s(c.width):c.width)/d,h=(f?s(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),h&&Number.isFinite(h)||(h=1),{x:g,y:h}}let at=u(0);function au(a){let b=V(a);return ai()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:at}function av(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=ar(a),h=u(1);b&&(d?Y(d)&&(h=as(d)):h=as(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===V(g))&&e)?au(g):u(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=V(g),b=d&&Y(d)?V(d):d,c=a,e=ap(c);for(;e&&d&&b!==c;){let a=as(e),b=e.getBoundingClientRect(),d=al(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=ap(c=V(e))}}return L({width:l,height:m,x:j,y:k})}function aw(a,b){let c=am(a).scrollLeft;return b?b.left+c:av(W(a)).left+c}function ax(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:aw(a,d)),y:d.top+b.scrollTop}}let ay=new Set(["absolute","fixed"]);function az(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=V(a),d=W(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=ai();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=W(a),c=am(a),d=a.ownerDocument.body,e=r(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=r(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+aw(a),h=-c.scrollTop;return"rtl"===al(d).direction&&(g+=r(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(W(a));else if(Y(b))d=function(a,b){let c=av(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=Z(a)?as(a):u(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=au(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return L(d)}function aA(a){return"static"===al(a).position}function aB(a,b){if(!Z(a)||"fixed"===al(a).position)return null;if(b)return b(a);let c=a.offsetParent;return W(a)===c&&(c=c.ownerDocument.body),c}function aC(a,b){var c;let d=V(a);if(ad(a))return d;if(!Z(a)){let b=an(a);for(;b&&!ak(b);){if(Y(b)&&!aA(b))return b;b=an(b)}return d}let e=aB(a,b);for(;e&&(c=e,ab.has(U(c)))&&aA(e);)e=aB(e,b);return e&&ak(e)&&aA(e)&&!ah(e)?d:e||function(a){let b=an(a);for(;Z(b)&&!ak(b);){if(ah(b))return b;if(ad(b))break;b=an(b)}return null}(a)||d}let aD=async function(a){let b=this.getOffsetParent||aC,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=Z(b),e=W(b),f="fixed"===c,g=av(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=u(0);if(d||!d&&!f)if(("body"!==U(b)||aa(e))&&(h=am(b)),d){let a=av(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=aw(e));f&&!d&&e&&(i.x=aw(e));let j=!e||d||f?u(0):ax(e,h);return{x:g.left+h.scrollLeft-i.x-j.x,y:g.top+h.scrollTop-i.y-j.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},aE={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=W(d),h=!!b&&ad(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},j=u(1),k=u(0),l=Z(d);if((l||!l&&!f)&&(("body"!==U(d)||aa(g))&&(i=am(d)),Z(d))){let a=av(d);j=as(d),k.x=a.x+d.clientLeft,k.y=a.y+d.clientTop}let m=!g||l||f?u(0):ax(g,i,!0);return{width:c.width*j.x,height:c.height*j.y,x:c.x*j.x-i.scrollLeft*j.x+k.x+m.x,y:c.y*j.y-i.scrollTop*j.y+k.y+m.y}},getDocumentElement:W,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?ad(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=ao(a,[],!1).filter(a=>Y(a)&&"body"!==U(a)),e=null,f="fixed"===al(a).position,g=f?an(a):a;for(;Y(g)&&!ak(g);){let b=al(g),c=ah(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&ay.has(e.position)||aa(g)&&!c&&function a(b,c){let d=an(b);return!(d===c||!Y(d)||ak(d))&&("fixed"===al(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=an(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=az(b,c,e);return a.top=r(d.top,a.top),a.right=q(d.right,a.right),a.bottom=q(d.bottom,a.bottom),a.left=r(d.left,a.left),a},az(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:aC,getElementRects:aD,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=aq(a);return{width:b,height:c}},getScale:as,isElement:Y,isRTL:function(a){return"rtl"===al(a).direction}};function aF(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let aG=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=x(a,b)||{};if(null==j)return{};let l=K(k),m={x:c,y:d},n=A(D(e)),o=B(n),p=await g.getDimensions(j),s="y"===n,t=s?"clientHeight":"clientWidth",u=f.reference[o]+f.reference[n]-m[n]-f.floating[o],v=m[n]-f.reference[n],w=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),y=w?w[t]:0;y&&await (null==g.isElement?void 0:g.isElement(w))||(y=h.floating[t]||f.floating[o]);let C=y/2-p[o]/2-1,E=q(l[s?"top":"left"],C),F=q(l[s?"bottom":"right"],C),G=y-p[o]-F,H=y/2-p[o]/2+(u/2-v/2),I=r(E,q(H,G)),J=!i.arrow&&null!=z(e)&&H!==I&&f.reference[o]/2-(H<E?E:F)-p[o]/2<0,L=J?H<E?H-E:H-G:0;return{[n]:m[n]+L,data:{[n]:I,centerOffset:H-I-L,...J&&{alignmentOffset:L}},reset:J}}});var aH=c(51215),aI="undefined"!=typeof document?d.useLayoutEffect:function(){};function aJ(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!aJ(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!aJ(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function aK(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function aL(a,b){let c=aK(a);return Math.round(b*c)/c}function aM(a){let b=d.useRef(a);return aI(()=>{b.current=a}),b}var aN=c(60687),aO=d.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,aN.jsx)(i.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,aN.jsx)("polygon",{points:"0,0 30,0 15,10"})})});aO.displayName="Arrow";var aP=c(13495),aQ=c(66156),aR="Popper",[aS,aT]=(0,g.A)(aR),[aU,aV]=aS(aR),aW=a=>{let{__scopePopper:b,children:c}=a,[e,f]=d.useState(null);return(0,aN.jsx)(aU,{scope:b,anchor:e,onAnchorChange:f,children:c})};aW.displayName=aR;var aX="PopperAnchor",aY=d.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:e,...g}=a,h=aV(aX,c),j=d.useRef(null),k=(0,f.s)(b,j);return d.useEffect(()=>{h.onAnchorChange(e?.current||j.current)}),e?null:(0,aN.jsx)(i.sG.div,{...g,ref:k})});aY.displayName=aX;var aZ="PopperContent",[a$,a_]=aS(aZ),a0=d.forwardRef((a,b)=>{let{__scopePopper:c,side:e="bottom",sideOffset:g=0,align:h="center",alignOffset:j=0,arrowPadding:k=0,avoidCollisions:l=!0,collisionBoundary:m=[],collisionPadding:n=0,sticky:o="partial",hideWhenDetached:p=!1,updatePositionStrategy:s="optimized",onPlaced:u,...v}=a,w=aV(aZ,c),[C,K]=d.useState(null),L=(0,f.s)(b,a=>K(a)),[M,T]=d.useState(null),U=function(a){let[b,c]=d.useState(void 0);return(0,aQ.N)(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}(M),V=U?.width??0,X=U?.height??0,Y="number"==typeof n?n:{top:0,right:0,bottom:0,left:0,...n},Z=Array.isArray(m)?m:[m],$=Z.length>0,_={padding:Y,boundary:Z.filter(a4),altBoundary:$},{refs:aa,floatingStyles:ab,placement:ac,isPositioned:ad,middlewareData:ae}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:e=[],platform:f,elements:{reference:g,floating:h}={},transform:i=!0,whileElementsMounted:j,open:k}=a,[l,m]=d.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[n,o]=d.useState(e);aJ(n,e)||o(e);let[p,q]=d.useState(null),[r,s]=d.useState(null),t=d.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=d.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=g||p,w=h||r,x=d.useRef(null),y=d.useRef(null),z=d.useRef(l),A=null!=j,B=aM(j),C=aM(f),D=aM(k),E=d.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:b,strategy:c,middleware:n};C.current&&(a.platform=C.current),((a,b,c)=>{let d=new Map,e={platform:aE,...c},f={...e.platform,_c:d};return N(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==D.current};F.current&&!aJ(z.current,b)&&(z.current=b,aH.flushSync(()=>{m(b)}))})},[n,b,c,C,D]);aI(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let F=d.useRef(!1);aI(()=>(F.current=!0,()=>{F.current=!1}),[]),aI(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(B.current)return B.current(v,w,E);E()}},[v,w,E,B,A]);let G=d.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),H=d.useMemo(()=>({reference:v,floating:w}),[v,w]),I=d.useMemo(()=>{let a={position:c,left:0,top:0};if(!H.floating)return a;let b=aL(H.floating,l.x),d=aL(H.floating,l.y);return i?{...a,transform:"translate("+b+"px, "+d+"px)",...aK(H.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,i,H.floating,l.x,l.y]);return d.useMemo(()=>({...l,update:E,refs:G,elements:H,floatingStyles:I}),[l,E,G,H,I])}({strategy:"fixed",placement:e+("center"!==h?"-"+h:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=ar(a),l=f||g?[...k?ao(k):[],...ao(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=W(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=t(l),p=t(e.clientWidth-(k+m)),s={rootMargin:-o+"px "+-p+"px "+-t(e.clientHeight-(l+n))+"px "+-t(k)+"px",threshold:r(0,q(1,i))||1},u=!0;function v(b){let d=b[0].intersectionRatio;if(d!==i){if(!u)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||aF(j,a.getBoundingClientRect())||g(),u=!1}try{d=new IntersectionObserver(v,{...s,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(v,s)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?av(a):null;return j&&function b(){let d=av(a);p&&!aF(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===s}),elements:{reference:w.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await S(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:g+X,alignmentAxis:j}),l&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=x(a,b),j={x:c,y:d},k=await O(b,i),l=D(y(e)),m=A(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=r(c,q(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=r(c,q(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===o?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=x(a,b),k={x:c,y:d},l=D(e),m=A(l),n=k[m],o=k[l],p=x(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=R.has(y(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(a),options:[a,b]}))():void 0,..._}),l&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=x(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=y(h),v=D(k),w=y(k)===k,C=await (null==l.isRTL?void 0:l.isRTL(m.floating)),K=p||(w||!s?[J(k)]:function(a){let b=J(a);return[E(a),b,E(b)]}(k)),L="none"!==r;!p&&L&&K.push(...function(a,b,c,d){let e=z(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?G:F;return b?F:G;case"left":case"right":return b?H:I;default:return[]}}(y(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(E)))),f}(k,s,r,C));let M=[k,...K],N=await O(b,t),P=[],Q=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&P.push(N[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=z(a),e=A(D(a)),f=B(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=J(g)),[g,J(g)]}(h,j,C);P.push(N[a[0]],N[a[1]])}if(Q=[...Q,{placement:h,overflows:P}],!P.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=M[a];if(b&&("alignment"!==o||v===D(b)||Q.every(a=>D(a.placement)!==v||a.overflows[0]>0)))return{data:{index:a,overflows:Q},reset:{placement:b}};let c=null==(f=Q.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=Q.filter(a=>{if(L){let b=D(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({..._}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=x(a,b),m=await O(b,l),n=y(g),o=z(g),p="y"===D(g),{width:s,height:t}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let u=t-m.top-m.bottom,v=s-m.left-m.right,w=q(t-m[e],u),A=q(s-m[f],v),B=!b.middlewareData.shift,C=w,E=A;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(E=v),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(C=u),B&&!o){let a=r(m.left,0),b=r(m.right,0),c=r(m.top,0),d=r(m.bottom,0);p?E=s-2*(0!==a||0!==b?a+b:r(m.left,m.right)):C=t-2*(0!==c||0!==d?c+d:r(m.top,m.bottom))}await k({...b,availableWidth:E,availableHeight:C});let F=await i.getDimensions(j.floating);return s!==F.width||t!==F.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({..._,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),M&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?aG({element:c.current,padding:d}).fn(b):{}:c?aG({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:M,padding:k}),a5({arrowWidth:V,arrowHeight:X}),p&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=x(a,b);switch(d){case"referenceHidden":{let a=P(await O(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:Q(a)}}}case"escaped":{let a=P(await O(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:Q(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",..._})]}),[af,ag]=a6(ac),ah=(0,aP.c)(u);(0,aQ.N)(()=>{ad&&ah?.()},[ad,ah]);let ai=ae.arrow?.x,aj=ae.arrow?.y,ak=ae.arrow?.centerOffset!==0,[al,am]=d.useState();return(0,aQ.N)(()=>{C&&am(window.getComputedStyle(C).zIndex)},[C]),(0,aN.jsx)("div",{ref:aa.setFloating,"data-radix-popper-content-wrapper":"",style:{...ab,transform:ad?ab.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:al,"--radix-popper-transform-origin":[ae.transformOrigin?.x,ae.transformOrigin?.y].join(" "),...ae.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,aN.jsx)(a$,{scope:c,placedSide:af,onArrowChange:T,arrowX:ai,arrowY:aj,shouldHideArrow:ak,children:(0,aN.jsx)(i.sG.div,{"data-side":af,"data-align":ag,...v,ref:L,style:{...v.style,animation:ad?void 0:"none"}})})})});a0.displayName=aZ;var a1="PopperArrow",a2={top:"bottom",right:"left",bottom:"top",left:"right"},a3=d.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=a_(a1,c),f=a2[e.placedSide];return(0,aN.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,aN.jsx)(aO,{...d,ref:b,style:{...d.style,display:"block"}})})});function a4(a){return null!==a}a3.displayName=a1;var a5=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=a6(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function a6(a){let[b,c="center"]=a.split("-");return[b,c]}var a7=c(25028),a8=c(46059),a9=c(72942),ba=c(8730),bb=c(63376),bc=c(42247),bd=["Enter"," "],be=["ArrowUp","PageDown","End"],bf=["ArrowDown","PageUp","Home",...be],bg={ltr:[...bd,"ArrowRight"],rtl:[...bd,"ArrowLeft"]},bh={ltr:["ArrowLeft"],rtl:["ArrowRight"]},bi="Menu",[bj,bk,bl]=(0,j.N)(bi),[bm,bn]=(0,g.A)(bi,[bl,aT,a9.RG]),bo=aT(),bp=(0,a9.RG)(),[bq,br]=bm(bi),[bs,bt]=bm(bi),bu=a=>{let{__scopeMenu:b,open:c=!1,children:e,dir:f,onOpenChange:g,modal:h=!0}=a,i=bo(b),[j,l]=d.useState(null),m=d.useRef(!1),n=(0,aP.c)(g),o=(0,k.jH)(f);return d.useEffect(()=>{let a=()=>{m.current=!0,document.addEventListener("pointerdown",b,{capture:!0,once:!0}),document.addEventListener("pointermove",b,{capture:!0,once:!0})},b=()=>m.current=!1;return document.addEventListener("keydown",a,{capture:!0}),()=>{document.removeEventListener("keydown",a,{capture:!0}),document.removeEventListener("pointerdown",b,{capture:!0}),document.removeEventListener("pointermove",b,{capture:!0})}},[]),(0,aN.jsx)(aW,{...i,children:(0,aN.jsx)(bq,{scope:b,open:c,onOpenChange:n,content:j,onContentChange:l,children:(0,aN.jsx)(bs,{scope:b,onClose:d.useCallback(()=>n(!1),[n]),isUsingKeyboardRef:m,dir:o,modal:h,children:e})})})};bu.displayName=bi;var bv=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=bo(c);return(0,aN.jsx)(aY,{...e,...d,ref:b})});bv.displayName="MenuAnchor";var bw="MenuPortal",[bx,by]=bm(bw,{forceMount:void 0}),bz=a=>{let{__scopeMenu:b,forceMount:c,children:d,container:e}=a,f=br(bw,b);return(0,aN.jsx)(bx,{scope:b,forceMount:c,children:(0,aN.jsx)(a8.C,{present:c||f.open,children:(0,aN.jsx)(a7.Z,{asChild:!0,container:e,children:d})})})};bz.displayName=bw;var bA="MenuContent",[bB,bC]=bm(bA),bD=d.forwardRef((a,b)=>{let c=by(bA,a.__scopeMenu),{forceMount:d=c.forceMount,...e}=a,f=br(bA,a.__scopeMenu),g=bt(bA,a.__scopeMenu);return(0,aN.jsx)(bj.Provider,{scope:a.__scopeMenu,children:(0,aN.jsx)(a8.C,{present:d||f.open,children:(0,aN.jsx)(bj.Slot,{scope:a.__scopeMenu,children:g.modal?(0,aN.jsx)(bE,{...e,ref:b}):(0,aN.jsx)(bF,{...e,ref:b})})})})}),bE=d.forwardRef((a,b)=>{let c=br(bA,a.__scopeMenu),g=d.useRef(null),h=(0,f.s)(b,g);return d.useEffect(()=>{let a=g.current;if(a)return(0,bb.Eq)(a)},[]),(0,aN.jsx)(bH,{...a,ref:h,trapFocus:c.open,disableOutsidePointerEvents:c.open,disableOutsideScroll:!0,onFocusOutside:(0,e.m)(a.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>c.onOpenChange(!1)})}),bF=d.forwardRef((a,b)=>{let c=br(bA,a.__scopeMenu);return(0,aN.jsx)(bH,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>c.onOpenChange(!1)})}),bG=(0,ba.TL)("MenuContent.ScrollLock"),bH=d.forwardRef((a,b)=>{let{__scopeMenu:c,loop:g=!1,trapFocus:h,onOpenAutoFocus:i,onCloseAutoFocus:j,disableOutsidePointerEvents:k,onEntryFocus:o,onEscapeKeyDown:p,onPointerDownOutside:q,onFocusOutside:r,onInteractOutside:s,onDismiss:t,disableOutsideScroll:u,...v}=a,w=br(bA,c),x=bt(bA,c),y=bo(c),z=bp(c),A=bk(c),[B,C]=d.useState(null),D=d.useRef(null),E=(0,f.s)(b,D,w.onContentChange),F=d.useRef(0),G=d.useRef(""),H=d.useRef(0),I=d.useRef(null),J=d.useRef("right"),K=d.useRef(0),L=u?bc.A:d.Fragment;d.useEffect(()=>()=>window.clearTimeout(F.current),[]),(0,m.Oh)();let M=d.useCallback(a=>J.current===I.current?.side&&function(a,b){return!!b&&function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}({x:a.clientX,y:a.clientY},b)}(a,I.current?.area),[]);return(0,aN.jsx)(bB,{scope:c,searchRef:G,onItemEnter:d.useCallback(a=>{M(a)&&a.preventDefault()},[M]),onItemLeave:d.useCallback(a=>{M(a)||(D.current?.focus(),C(null))},[M]),onTriggerLeave:d.useCallback(a=>{M(a)&&a.preventDefault()},[M]),pointerGraceTimerRef:H,onPointerGraceIntentChange:d.useCallback(a=>{I.current=a},[]),children:(0,aN.jsx)(L,{...u?{as:bG,allowPinchZoom:!0}:void 0,children:(0,aN.jsx)(n.n,{asChild:!0,trapped:h,onMountAutoFocus:(0,e.m)(i,a=>{a.preventDefault(),D.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:j,children:(0,aN.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:k,onEscapeKeyDown:p,onPointerDownOutside:q,onFocusOutside:r,onInteractOutside:s,onDismiss:t,children:(0,aN.jsx)(a9.bL,{asChild:!0,...z,dir:x.dir,orientation:"vertical",loop:g,currentTabStopId:B,onCurrentTabStopIdChange:C,onEntryFocus:(0,e.m)(o,a=>{x.isUsingKeyboardRef.current||a.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,aN.jsx)(a0,{role:"menu","aria-orientation":"vertical","data-state":b5(w.open),"data-radix-menu-content":"",dir:x.dir,...y,...v,ref:E,style:{outline:"none",...v.style},onKeyDown:(0,e.m)(v.onKeyDown,a=>{let b=a.target.closest("[data-radix-menu-content]")===a.currentTarget,c=a.ctrlKey||a.altKey||a.metaKey,d=1===a.key.length;b&&("Tab"===a.key&&a.preventDefault(),!c&&d&&(a=>{let b=G.current+a,c=A().filter(a=>!a.disabled),d=document.activeElement,e=c.find(a=>a.ref.current===d)?.textValue,f=function(a,b,c){var d;let e=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,f=c?a.indexOf(c):-1,g=(d=Math.max(f,0),a.map((b,c)=>a[(d+c)%a.length]));1===e.length&&(g=g.filter(a=>a!==c));let h=g.find(a=>a.toLowerCase().startsWith(e.toLowerCase()));return h!==c?h:void 0}(c.map(a=>a.textValue),b,e),g=c.find(a=>a.textValue===f)?.ref.current;!function a(b){G.current=b,window.clearTimeout(F.current),""!==b&&(F.current=window.setTimeout(()=>a(""),1e3))}(b),g&&setTimeout(()=>g.focus())})(a.key));let e=D.current;if(a.target!==e||!bf.includes(a.key))return;a.preventDefault();let f=A().filter(a=>!a.disabled).map(a=>a.ref.current);be.includes(a.key)&&f.reverse(),function(a){let b=document.activeElement;for(let c of a)if(c===b||(c.focus(),document.activeElement!==b))return}(f)}),onBlur:(0,e.m)(a.onBlur,a=>{a.currentTarget.contains(a.target)||(window.clearTimeout(F.current),G.current="")}),onPointerMove:(0,e.m)(a.onPointerMove,b8(a=>{let b=a.target,c=K.current!==a.clientX;a.currentTarget.contains(b)&&c&&(J.current=a.clientX>K.current?"right":"left",K.current=a.clientX)}))})})})})})})});bD.displayName=bA;var bI=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,aN.jsx)(i.sG.div,{role:"group",...d,ref:b})});bI.displayName="MenuGroup";var bJ=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,aN.jsx)(i.sG.div,{...d,ref:b})});bJ.displayName="MenuLabel";var bK="MenuItem",bL="menu.itemSelect",bM=d.forwardRef((a,b)=>{let{disabled:c=!1,onSelect:g,...h}=a,j=d.useRef(null),k=bt(bK,a.__scopeMenu),l=bC(bK,a.__scopeMenu),m=(0,f.s)(b,j),n=d.useRef(!1);return(0,aN.jsx)(bN,{...h,ref:m,disabled:c,onClick:(0,e.m)(a.onClick,()=>{let a=j.current;if(!c&&a){let b=new CustomEvent(bL,{bubbles:!0,cancelable:!0});a.addEventListener(bL,a=>g?.(a),{once:!0}),(0,i.hO)(a,b),b.defaultPrevented?n.current=!1:k.onClose()}}),onPointerDown:b=>{a.onPointerDown?.(b),n.current=!0},onPointerUp:(0,e.m)(a.onPointerUp,a=>{n.current||a.currentTarget?.click()}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{let b=""!==l.searchRef.current;c||b&&" "===a.key||bd.includes(a.key)&&(a.currentTarget.click(),a.preventDefault())})})});bM.displayName=bK;var bN=d.forwardRef((a,b)=>{let{__scopeMenu:c,disabled:g=!1,textValue:h,...j}=a,k=bC(bK,c),l=bp(c),m=d.useRef(null),n=(0,f.s)(b,m),[o,p]=d.useState(!1),[q,r]=d.useState("");return d.useEffect(()=>{let a=m.current;a&&r((a.textContent??"").trim())},[j.children]),(0,aN.jsx)(bj.ItemSlot,{scope:c,disabled:g,textValue:h??q,children:(0,aN.jsx)(a9.q7,{asChild:!0,...l,focusable:!g,children:(0,aN.jsx)(i.sG.div,{role:"menuitem","data-highlighted":o?"":void 0,"aria-disabled":g||void 0,"data-disabled":g?"":void 0,...j,ref:n,onPointerMove:(0,e.m)(a.onPointerMove,b8(a=>{g?k.onItemLeave(a):(k.onItemEnter(a),a.defaultPrevented||a.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,e.m)(a.onPointerLeave,b8(a=>k.onItemLeave(a))),onFocus:(0,e.m)(a.onFocus,()=>p(!0)),onBlur:(0,e.m)(a.onBlur,()=>p(!1))})})})}),bO=d.forwardRef((a,b)=>{let{checked:c=!1,onCheckedChange:d,...f}=a;return(0,aN.jsx)(bW,{scope:a.__scopeMenu,checked:c,children:(0,aN.jsx)(bM,{role:"menuitemcheckbox","aria-checked":b6(c)?"mixed":c,...f,ref:b,"data-state":b7(c),onSelect:(0,e.m)(f.onSelect,()=>d?.(!!b6(c)||!c),{checkForDefaultPrevented:!1})})})});bO.displayName="MenuCheckboxItem";var bP="MenuRadioGroup",[bQ,bR]=bm(bP,{value:void 0,onValueChange:()=>{}}),bS=d.forwardRef((a,b)=>{let{value:c,onValueChange:d,...e}=a,f=(0,aP.c)(d);return(0,aN.jsx)(bQ,{scope:a.__scopeMenu,value:c,onValueChange:f,children:(0,aN.jsx)(bI,{...e,ref:b})})});bS.displayName=bP;var bT="MenuRadioItem",bU=d.forwardRef((a,b)=>{let{value:c,...d}=a,f=bR(bT,a.__scopeMenu),g=c===f.value;return(0,aN.jsx)(bW,{scope:a.__scopeMenu,checked:g,children:(0,aN.jsx)(bM,{role:"menuitemradio","aria-checked":g,...d,ref:b,"data-state":b7(g),onSelect:(0,e.m)(d.onSelect,()=>f.onValueChange?.(c),{checkForDefaultPrevented:!1})})})});bU.displayName=bT;var bV="MenuItemIndicator",[bW,bX]=bm(bV,{checked:!1}),bY=d.forwardRef((a,b)=>{let{__scopeMenu:c,forceMount:d,...e}=a,f=bX(bV,c);return(0,aN.jsx)(a8.C,{present:d||b6(f.checked)||!0===f.checked,children:(0,aN.jsx)(i.sG.span,{...e,ref:b,"data-state":b7(f.checked)})})});bY.displayName=bV;var bZ=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,aN.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...d,ref:b})});bZ.displayName="MenuSeparator";var b$=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=bo(c);return(0,aN.jsx)(a3,{...e,...d,ref:b})});b$.displayName="MenuArrow";var[b_,b0]=bm("MenuSub"),b1="MenuSubTrigger",b2=d.forwardRef((a,b)=>{let c=br(b1,a.__scopeMenu),g=bt(b1,a.__scopeMenu),h=b0(b1,a.__scopeMenu),i=bC(b1,a.__scopeMenu),j=d.useRef(null),{pointerGraceTimerRef:k,onPointerGraceIntentChange:l}=i,m={__scopeMenu:a.__scopeMenu},n=d.useCallback(()=>{j.current&&window.clearTimeout(j.current),j.current=null},[]);return d.useEffect(()=>n,[n]),d.useEffect(()=>{let a=k.current;return()=>{window.clearTimeout(a),l(null)}},[k,l]),(0,aN.jsx)(bv,{asChild:!0,...m,children:(0,aN.jsx)(bN,{id:h.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":h.contentId,"data-state":b5(c.open),...a,ref:(0,f.t)(b,h.onTriggerChange),onClick:b=>{a.onClick?.(b),a.disabled||b.defaultPrevented||(b.currentTarget.focus(),c.open||c.onOpenChange(!0))},onPointerMove:(0,e.m)(a.onPointerMove,b8(b=>{i.onItemEnter(b),!b.defaultPrevented&&(a.disabled||c.open||j.current||(i.onPointerGraceIntentChange(null),j.current=window.setTimeout(()=>{c.onOpenChange(!0),n()},100)))})),onPointerLeave:(0,e.m)(a.onPointerLeave,b8(a=>{n();let b=c.content?.getBoundingClientRect();if(b){let d=c.content?.dataset.side,e="right"===d,f=b[e?"left":"right"],g=b[e?"right":"left"];i.onPointerGraceIntentChange({area:[{x:a.clientX+(e?-5:5),y:a.clientY},{x:f,y:b.top},{x:g,y:b.top},{x:g,y:b.bottom},{x:f,y:b.bottom}],side:d}),window.clearTimeout(k.current),k.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(a),a.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,e.m)(a.onKeyDown,b=>{let d=""!==i.searchRef.current;a.disabled||d&&" "===b.key||bg[g.dir].includes(b.key)&&(c.onOpenChange(!0),c.content?.focus(),b.preventDefault())})})})});b2.displayName=b1;var b3="MenuSubContent",b4=d.forwardRef((a,b)=>{let c=by(bA,a.__scopeMenu),{forceMount:g=c.forceMount,...h}=a,i=br(bA,a.__scopeMenu),j=bt(bA,a.__scopeMenu),k=b0(b3,a.__scopeMenu),l=d.useRef(null),m=(0,f.s)(b,l);return(0,aN.jsx)(bj.Provider,{scope:a.__scopeMenu,children:(0,aN.jsx)(a8.C,{present:g||i.open,children:(0,aN.jsx)(bj.Slot,{scope:a.__scopeMenu,children:(0,aN.jsx)(bH,{id:k.contentId,"aria-labelledby":k.triggerId,...h,ref:m,align:"start",side:"rtl"===j.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:a=>{j.isUsingKeyboardRef.current&&l.current?.focus(),a.preventDefault()},onCloseAutoFocus:a=>a.preventDefault(),onFocusOutside:(0,e.m)(a.onFocusOutside,a=>{a.target!==k.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,e.m)(a.onEscapeKeyDown,a=>{j.onClose(),a.preventDefault()}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{let b=a.currentTarget.contains(a.target),c=bh[j.dir].includes(a.key);b&&c&&(i.onOpenChange(!1),k.trigger?.focus(),a.preventDefault())})})})})})});function b5(a){return a?"open":"closed"}function b6(a){return"indeterminate"===a}function b7(a){return b6(a)?"indeterminate":a?"checked":"unchecked"}function b8(a){return b=>"mouse"===b.pointerType?a(b):void 0}b4.displayName=b3;var b9="DropdownMenu",[ca,cb]=(0,g.A)(b9,[bn]),cc=bn(),[cd,ce]=ca(b9),cf=a=>{let{__scopeDropdownMenu:b,children:c,dir:e,open:f,defaultOpen:g,onOpenChange:i,modal:j=!0}=a,k=cc(b),l=d.useRef(null),[m,n]=(0,h.i)({prop:f,defaultProp:g??!1,onChange:i,caller:b9});return(0,aN.jsx)(cd,{scope:b,triggerId:(0,o.B)(),triggerRef:l,contentId:(0,o.B)(),open:m,onOpenChange:n,onOpenToggle:d.useCallback(()=>n(a=>!a),[n]),modal:j,children:(0,aN.jsx)(bu,{...k,open:m,onOpenChange:n,dir:e,modal:j,children:c})})};cf.displayName=b9;var cg="DropdownMenuTrigger",ch=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,disabled:d=!1,...g}=a,h=ce(cg,c),j=cc(c);return(0,aN.jsx)(bv,{asChild:!0,...j,children:(0,aN.jsx)(i.sG.button,{type:"button",id:h.triggerId,"aria-haspopup":"menu","aria-expanded":h.open,"aria-controls":h.open?h.contentId:void 0,"data-state":h.open?"open":"closed","data-disabled":d?"":void 0,disabled:d,...g,ref:(0,f.t)(b,h.triggerRef),onPointerDown:(0,e.m)(a.onPointerDown,a=>{!d&&0===a.button&&!1===a.ctrlKey&&(h.onOpenToggle(),h.open||a.preventDefault())}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{!d&&(["Enter"," "].includes(a.key)&&h.onOpenToggle(),"ArrowDown"===a.key&&h.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});ch.displayName=cg;var ci=a=>{let{__scopeDropdownMenu:b,...c}=a,d=cc(b);return(0,aN.jsx)(bz,{...d,...c})};ci.displayName="DropdownMenuPortal";var cj="DropdownMenuContent",ck=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...f}=a,g=ce(cj,c),h=cc(c),i=d.useRef(!1);return(0,aN.jsx)(bD,{id:g.contentId,"aria-labelledby":g.triggerId,...h,...f,ref:b,onCloseAutoFocus:(0,e.m)(a.onCloseAutoFocus,a=>{i.current||g.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:(0,e.m)(a.onInteractOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey,d=2===b.button||c;(!g.modal||d)&&(i.current=!0)}),style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ck.displayName=cj,d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cc(c);return(0,aN.jsx)(bI,{...e,...d,ref:b})}).displayName="DropdownMenuGroup";var cl=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cc(c);return(0,aN.jsx)(bJ,{...e,...d,ref:b})});cl.displayName="DropdownMenuLabel";var cm=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cc(c);return(0,aN.jsx)(bM,{...e,...d,ref:b})});cm.displayName="DropdownMenuItem",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cc(c);return(0,aN.jsx)(bO,{...e,...d,ref:b})}).displayName="DropdownMenuCheckboxItem",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cc(c);return(0,aN.jsx)(bS,{...e,...d,ref:b})}).displayName="DropdownMenuRadioGroup",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cc(c);return(0,aN.jsx)(bU,{...e,...d,ref:b})}).displayName="DropdownMenuRadioItem",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cc(c);return(0,aN.jsx)(bY,{...e,...d,ref:b})}).displayName="DropdownMenuItemIndicator";var cn=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cc(c);return(0,aN.jsx)(bZ,{...e,...d,ref:b})});cn.displayName="DropdownMenuSeparator",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cc(c);return(0,aN.jsx)(b$,{...e,...d,ref:b})}).displayName="DropdownMenuArrow",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cc(c);return(0,aN.jsx)(b2,{...e,...d,ref:b})}).displayName="DropdownMenuSubTrigger",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=cc(c);return(0,aN.jsx)(b4,{...e,...d,ref:b,style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var co=cf,cp=ch,cq=ci,cr=ck,cs=cl,ct=cm,cu=cn},57379:(a,b,c)=>{a.exports=c(53332)},58869:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63376:(a,b,c)=>{c.d(b,{Eq:()=>j});var d=new WeakMap,e=new WeakMap,f={},g=0,h=function(a){return a&&(a.host||h(a.parentNode))},i=function(a,b,c,i){var j=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=h(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});f[c]||(f[c]=new WeakMap);var k=f[c],l=[],m=new Set,n=new Set(j),o=function(a){!a||m.has(a)||(m.add(a),o(a.parentNode))};j.forEach(o);var p=function(a){!a||n.has(a)||Array.prototype.forEach.call(a.children,function(a){if(m.has(a))p(a);else try{var b=a.getAttribute(i),f=null!==b&&"false"!==b,g=(d.get(a)||0)+1,h=(k.get(a)||0)+1;d.set(a,g),k.set(a,h),l.push(a),1===g&&f&&e.set(a,!0),1===h&&a.setAttribute(c,"true"),f||a.setAttribute(i,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return p(b),m.clear(),g++,function(){l.forEach(function(a){var b=d.get(a)-1,f=k.get(a)-1;d.set(a,b),k.set(a,f),b||(e.has(a)||a.removeAttribute(i),e.delete(a)),f||a.removeAttribute(c)}),--g||(d=new WeakMap,d=new WeakMap,e=new WeakMap,f={})}},j=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),i(d,e,c,"aria-hidden")):function(){return null}}},65551:(a,b,c)=>{c.d(b,{i:()=>h});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useInsertionEffect ".trim().toString()]||f.N;function h({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[f,h,i]=function({defaultProp:a,onChange:b}){let[c,d]=e.useState(a),f=e.useRef(c),h=e.useRef(b);return g(()=>{h.current=b},[b]),e.useEffect(()=>{f.current!==c&&(h.current?.(c),f.current=c)},[c,f]),[c,d,h]}({defaultProp:b,onChange:c}),j=void 0!==a,k=j?a:f;{let b=e.useRef(void 0!==a);e.useEffect(()=>{let a=b.current;if(a!==j){let b=j?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=j},[j,d])}return[k,e.useCallback(b=>{if(j){let c="function"==typeof b?b(a):b;c!==a&&i.current?.(c)}else h(b)},[j,a,h,i])]}Symbol("RADIX:SYNC_STATE")},66156:(a,b,c)=>{c.d(b,{N:()=>e});var d=c(43210),e=globalThis?.document?d.useLayoutEffect:()=>{}},70569:(a,b,c)=>{c.d(b,{m:()=>d});function d(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}},72942:(a,b,c)=>{c.d(b,{RG:()=>v,bL:()=>E,q7:()=>F});var d=c(43210),e=c(70569),f=c(9510),g=c(98599),h=c(11273),i=c(96963),j=c(14163),k=c(13495),l=c(65551),m=c(43),n=c(60687),o="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},q="RovingFocusGroup",[r,s,t]=(0,f.N)(q),[u,v]=(0,h.A)(q,[t]),[w,x]=u(q),y=d.forwardRef((a,b)=>(0,n.jsx)(r.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,n.jsx)(r.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,n.jsx)(z,{...a,ref:b})})}));y.displayName=q;var z=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:f,loop:h=!1,dir:i,currentTabStopId:r,defaultCurrentTabStopId:t,onCurrentTabStopIdChange:u,onEntryFocus:v,preventScrollOnEntryFocus:x=!1,...y}=a,z=d.useRef(null),A=(0,g.s)(b,z),B=(0,m.jH)(i),[C,E]=(0,l.i)({prop:r,defaultProp:t??null,onChange:u,caller:q}),[F,G]=d.useState(!1),H=(0,k.c)(v),I=s(c),J=d.useRef(!1),[K,L]=d.useState(0);return d.useEffect(()=>{let a=z.current;if(a)return a.addEventListener(o,H),()=>a.removeEventListener(o,H)},[H]),(0,n.jsx)(w,{scope:c,orientation:f,dir:B,loop:h,currentTabStopId:C,onItemFocus:d.useCallback(a=>E(a),[E]),onItemShiftTab:d.useCallback(()=>G(!0),[]),onFocusableItemAdd:d.useCallback(()=>L(a=>a+1),[]),onFocusableItemRemove:d.useCallback(()=>L(a=>a-1),[]),children:(0,n.jsx)(j.sG.div,{tabIndex:F||0===K?-1:0,"data-orientation":f,...y,ref:A,style:{outline:"none",...a.style},onMouseDown:(0,e.m)(a.onMouseDown,()=>{J.current=!0}),onFocus:(0,e.m)(a.onFocus,a=>{let b=!J.current;if(a.target===a.currentTarget&&b&&!F){let b=new CustomEvent(o,p);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=I().filter(a=>a.focusable);D([a.find(a=>a.active),a.find(a=>a.id===C),...a].filter(Boolean).map(a=>a.ref.current),x)}}J.current=!1}),onBlur:(0,e.m)(a.onBlur,()=>G(!1))})})}),A="RovingFocusGroupItem",B=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:f=!0,active:g=!1,tabStopId:h,children:k,...l}=a,m=(0,i.B)(),o=h||m,p=x(A,c),q=p.currentTabStopId===o,t=s(c),{onFocusableItemAdd:u,onFocusableItemRemove:v,currentTabStopId:w}=p;return d.useEffect(()=>{if(f)return u(),()=>v()},[f,u,v]),(0,n.jsx)(r.ItemSlot,{scope:c,id:o,focusable:f,active:g,children:(0,n.jsx)(j.sG.span,{tabIndex:q?0:-1,"data-orientation":p.orientation,...l,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f?p.onItemFocus(o):a.preventDefault()}),onFocus:(0,e.m)(a.onFocus,()=>p.onItemFocus(o)),onKeyDown:(0,e.m)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void p.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return C[e]}(a,p.orientation,p.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=t().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=p.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>D(c))}}),children:"function"==typeof k?k({isCurrentTabStop:q,hasTabStop:null!=w}):k})})});B.displayName=A;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var E=y,F=B},84027:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},90103:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("clipboard-check",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]])},96963:(a,b,c)=>{c.d(b,{B:()=>i});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useId ".trim().toString()]||(()=>void 0),h=0;function i(a){let[b,c]=e.useState(g());return(0,f.N)(()=>{a||c(a=>a??String(h++))},[a]),a||(b?`radix-${b}`:"")}}};