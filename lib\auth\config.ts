import { NextAuthConfig } from "next-auth"
import Credentials from "next-auth/providers/credentials"

// Mock user data - in production, this would come from a database
const users = [
  {
    id: "1",
    email: "<EMAIL>",
    password: "admin123", // In production, this should be hashed
    name: "Administrator",
    role: "admin",
  },
  {
    id: "2", 
    email: "<EMAIL>",
    password: "teacher123", // In production, this should be hashed
    name: "Teacher",
    role: "teacher",
  },
]

export const authConfig: NextAuthConfig = {
  pages: {
    signIn: "/login",
  },
  providers: [
    Credentials({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = users.find(
          (user) => user.email === credentials.email
        )

        if (!user) {
          return null
        }

        // In production, compare hashed passwords
        if (user.password !== credentials.password) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as string
      }
      return session
    },
  },
  session: {
    strategy: "jwt",
  },
}
