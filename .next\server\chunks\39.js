"use strict";exports.id=39,exports.ids=[39],exports.modules={163:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=c(71042).unstable_rethrow;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},10189:(a,b,c)=>{c.d(b,{A:()=>d});function d(a){return{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:a}}},19443:(a,b,c)=>{let d,e,f,g,h;c.d(b,{Ay:()=>fK});var i={};c.r(i),c.d(i,{q:()=>bL,l:()=>bO});var j=function(a,b,c,d,e){if("m"===d)throw TypeError("Private method is not writable");if("a"===d&&!e)throw TypeError("Private accessor was defined without a setter");if("function"==typeof b?a!==b||!e:!b.has(a))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===d?e.call(a,c):e?e.value=c:b.set(a,c),c},k=function(a,b,c,d){if("a"===c&&!d)throw TypeError("Private accessor was defined without a getter");if("function"==typeof b?a!==b||!d:!b.has(a))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===c?d:"a"===c?d.call(a):d?d.value:b.get(a)};function l(a){let b=a?"__Secure-":"";return{sessionToken:{name:`${b}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},callbackUrl:{name:`${b}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},csrfToken:{name:`${a?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},pkceCodeVerifier:{name:`${b}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},state:{name:`${b}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},nonce:{name:`${b}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},webauthnChallenge:{name:`${b}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}}}}class m{constructor(a,b,c){if(ck.add(this),cl.set(this,{}),cm.set(this,void 0),cn.set(this,void 0),j(this,cn,c,"f"),j(this,cm,a,"f"),!b)return;let{name:d}=a;for(let[a,c]of Object.entries(b))a.startsWith(d)&&c&&(k(this,cl,"f")[a]=c)}get value(){return Object.keys(k(this,cl,"f")).sort((a,b)=>parseInt(a.split(".").pop()||"0")-parseInt(b.split(".").pop()||"0")).map(a=>k(this,cl,"f")[a]).join("")}chunk(a,b){let c=k(this,ck,"m",cp).call(this);for(let d of k(this,ck,"m",co).call(this,{name:k(this,cm,"f").name,value:a,options:{...k(this,cm,"f").options,...b}}))c[d.name]=d;return Object.values(c)}clean(){return Object.values(k(this,ck,"m",cp).call(this))}}cl=new WeakMap,cm=new WeakMap,cn=new WeakMap,ck=new WeakSet,co=function(a){let b=Math.ceil(a.value.length/3936);if(1===b)return k(this,cl,"f")[a.name]=a.value,[a];let c=[];for(let d=0;d<b;d++){let b=`${a.name}.${d}`,e=a.value.substr(3936*d,3936);c.push({...a,name:b,value:e}),k(this,cl,"f")[b]=e}return k(this,cn,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:a.value.length,chunks:c.map(a=>a.value.length+160)}),c},cp=function(){let a={};for(let b in k(this,cl,"f"))delete k(this,cl,"f")?.[b],a[b]={name:b,value:"",options:{...k(this,cm,"f").options,maxAge:0}};return a};class n extends Error{constructor(a,b){a instanceof Error?super(void 0,{cause:{err:a,...a.cause,...b}}):"string"==typeof a?(b instanceof Error&&(b={err:b,...b.cause}),super(a,b)):super(void 0,a),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let c=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${c}`}}class o extends n{}o.kind="signIn";class p extends n{}p.type="AdapterError";class q extends n{}q.type="AccessDenied";class r extends n{}r.type="CallbackRouteError";class s extends n{}s.type="ErrorPageLoop";class t extends n{}t.type="EventError";class u extends n{}u.type="InvalidCallbackUrl";class v extends o{constructor(){super(...arguments),this.code="credentials"}}v.type="CredentialsSignin";class w extends n{}w.type="InvalidEndpoints";class x extends n{}x.type="InvalidCheck";class y extends n{}y.type="JWTSessionError";class z extends n{}z.type="MissingAdapter";class A extends n{}A.type="MissingAdapterMethods";class B extends n{}B.type="MissingAuthorize";class C extends n{}C.type="MissingSecret";class D extends o{}D.type="OAuthAccountNotLinked";class E extends o{}E.type="OAuthCallbackError";class F extends n{}F.type="OAuthProfileParseError";class G extends n{}G.type="SessionTokenError";class H extends o{}H.type="OAuthSignInError";class I extends o{}I.type="EmailSignInError";class J extends n{}J.type="SignOutError";class K extends n{}K.type="UnknownAction";class L extends n{}L.type="UnsupportedStrategy";class M extends n{}M.type="InvalidProvider";class N extends n{}N.type="UntrustedHost";class O extends n{}O.type="Verification";class P extends o{}P.type="MissingCSRF";let Q=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class R extends n{}R.type="DuplicateConditionalUI";class S extends n{}S.type="MissingWebAuthnAutocomplete";class T extends n{}T.type="WebAuthnVerificationError";class U extends o{}U.type="AccountNotLinked";class V extends n{}V.type="ExperimentalFeatureNotEnabled";let W=!1;function X(a,b){try{return/^https?:/.test(new URL(a,a.startsWith("/")?b:void 0).protocol)}catch{return!1}}let Y=!1,Z=!1,$=!1,_=["createVerificationToken","useVerificationToken","getUserByEmail"],aa=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],ab=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var ac=c(55511);"function"!=typeof ac.hkdf||process.versions.electron||(d=async(...a)=>new Promise((b,c)=>{ac.hkdf(...a,(a,d)=>{a?c(a):b(new Uint8Array(d))})}));let ad=async(a,b,c,e,f)=>(d||((a,b,c,d,e)=>{let f=parseInt(a.substr(3),10)>>3||20,g=(0,ac.createHmac)(a,c.byteLength?c:new Uint8Array(f)).update(b).digest(),h=Math.ceil(e/f),i=new Uint8Array(f*h+d.byteLength+1),j=0,k=0;for(let b=1;b<=h;b++)i.set(d,k),i[k+d.byteLength]=b,i.set((0,ac.createHmac)(a,g).update(i.subarray(j,k+d.byteLength+1)).digest(),k),j=k,k+=f;return i.slice(0,e)}))(a,b,c,e,f);function ae(a,b){if("string"==typeof a)return new TextEncoder().encode(a);if(!(a instanceof Uint8Array))throw TypeError(`"${b}"" must be an instance of Uint8Array or a string`);return a}async function af(a,b,c,d,e){return ad(function(a){switch(a){case"sha256":case"sha384":case"sha512":case"sha1":return a;default:throw TypeError('unsupported "digest" value')}}(a),function(a){let b=ae(a,"ikm");if(!b.byteLength)throw TypeError('"ikm" must be at least one byte in length');return b}(b),ae(c,"salt"),function(a){let b=ae(a,"info");if(b.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return b}(d),function(a,b){if("number"!=typeof a||!Number.isInteger(a)||a<1)throw TypeError('"keylen" must be a positive integer');if(a>255*(parseInt(b.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return a}(e,a))}let ag=async(a,b)=>{let c=`SHA-${a.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(c,b))},ah=new TextEncoder,ai=new TextDecoder;function aj(...a){let b=new Uint8Array(a.reduce((a,{length:b})=>a+b,0)),c=0;for(let d of a)b.set(d,c),c+=d.length;return b}function ak(a,b,c){if(b<0||b>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${b}`);a.set([b>>>24,b>>>16,b>>>8,255&b],c)}function al(a){let b=Math.floor(a/0x100000000),c=new Uint8Array(8);return ak(c,b,0),ak(c,a%0x100000000,4),c}function am(a){let b=new Uint8Array(4);return ak(b,a),b}function an(a){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof a?a:ai.decode(a),{alphabet:"base64url"});let b=a;b instanceof Uint8Array&&(b=ai.decode(b)),b=b.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var c=b;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(c);let a=atob(c),d=new Uint8Array(a.length);for(let b=0;b<a.length;b++)d[b]=a.charCodeAt(b);return d}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function ao(a){let b=a;return("string"==typeof b&&(b=ah.encode(b)),Uint8Array.prototype.toBase64)?b.toBase64({alphabet:"base64url",omitPadding:!0}):(function(a){if(Uint8Array.prototype.toBase64)return a.toBase64();let b=[];for(let c=0;c<a.length;c+=32768)b.push(String.fromCharCode.apply(null,a.subarray(c,c+32768)));return btoa(b.join(""))})(b).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class ap extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(a,b){super(a,b),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class aq extends ap{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(a,b,c="unspecified",d="unspecified"){super(a,{cause:{claim:c,reason:d,payload:b}}),this.claim=c,this.reason=d,this.payload=b}}class ar extends ap{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(a,b,c="unspecified",d="unspecified"){super(a,{cause:{claim:c,reason:d,payload:b}}),this.claim=c,this.reason=d,this.payload=b}}class as extends ap{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class at extends ap{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class au extends ap{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(a="decryption operation failed",b){super(a,b)}}class av extends ap{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class aw extends ap{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class ax extends ap{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class ay extends ap{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(a="multiple matching keys found in the JSON Web Key Set",b){super(a,b)}}function az(a){if(!aA(a))throw Error("CryptoKey instance expected")}function aA(a){return a?.[Symbol.toStringTag]==="CryptoKey"}function aB(a){return a?.[Symbol.toStringTag]==="KeyObject"}let aC=a=>aA(a)||aB(a),aD=a=>{if(!function(a){return"object"==typeof a&&null!==a}(a)||"[object Object]"!==Object.prototype.toString.call(a))return!1;if(null===Object.getPrototypeOf(a))return!0;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b};function aE(a){return aD(a)&&"string"==typeof a.kty}function aF(a,b,...c){if((c=c.filter(Boolean)).length>2){let b=c.pop();a+=`one of type ${c.join(", ")}, or ${b}.`}else 2===c.length?a+=`one of type ${c[0]} or ${c[1]}.`:a+=`of type ${c[0]}.`;return null==b?a+=` Received ${b}`:"function"==typeof b&&b.name?a+=` Received function ${b.name}`:"object"==typeof b&&null!=b&&b.constructor?.name&&(a+=` Received an instance of ${b.constructor.name}`),a}let aG=(a,...b)=>aF("Key must be ",a,...b);function aH(a,b,...c){return aF(`Key for the ${a} algorithm must be `,b,...c)}async function aI(a){if(aB(a))if("secret"!==a.type)return a.export({format:"jwk"});else a=a.export();if(a instanceof Uint8Array)return{kty:"oct",k:ao(a)};if(!aA(a))throw TypeError(aG(a,"CryptoKey","KeyObject","Uint8Array"));if(!a.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:b,key_ops:c,alg:d,use:e,...f}=await crypto.subtle.exportKey("jwk",a);return f}async function aJ(a){return aI(a)}let aK=(a,b)=>{if("string"!=typeof a||!a)throw new ax(`${b} missing or invalid`)};async function aL(a,b){let c,d;if(aE(a))c=a;else if(aC(a))c=await aJ(a);else throw TypeError(aG(a,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(b??="sha256")&&"sha384"!==b&&"sha512"!==b)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(c.kty){case"EC":aK(c.crv,'"crv" (Curve) Parameter'),aK(c.x,'"x" (X Coordinate) Parameter'),aK(c.y,'"y" (Y Coordinate) Parameter'),d={crv:c.crv,kty:c.kty,x:c.x,y:c.y};break;case"OKP":aK(c.crv,'"crv" (Subtype of Key Pair) Parameter'),aK(c.x,'"x" (Public Key) Parameter'),d={crv:c.crv,kty:c.kty,x:c.x};break;case"RSA":aK(c.e,'"e" (Exponent) Parameter'),aK(c.n,'"n" (Modulus) Parameter'),d={e:c.e,kty:c.kty,n:c.n};break;case"oct":aK(c.k,'"k" (Key Value) Parameter'),d={k:c.k,kty:c.kty};break;default:throw new at('"kty" (Key Type) Parameter missing or unsupported')}let e=ah.encode(JSON.stringify(d));return ao(await ag(b,e))}let aM=Symbol();function aN(a){switch(a){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new at(`Unsupported JWE Algorithm: ${a}`)}}let aO=(a,b)=>{if(b.length<<3!==aN(a))throw new av("Invalid Initialization Vector length")},aP=(a,b)=>{let c=a.byteLength<<3;if(c!==b)throw new av(`Invalid Content Encryption Key length. Expected ${b} bits, got ${c} bits`)};function aQ(a,b="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${b} must be ${a}`)}function aR(a,b){return a.name===b}function aS(a,b,c){switch(b){case"A128GCM":case"A192GCM":case"A256GCM":{if(!aR(a.algorithm,"AES-GCM"))throw aQ("AES-GCM");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw aQ(c,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!aR(a.algorithm,"AES-KW"))throw aQ("AES-KW");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw aQ(c,"algorithm.length");break}case"ECDH":switch(a.algorithm.name){case"ECDH":case"X25519":break;default:throw aQ("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!aR(a.algorithm,"PBKDF2"))throw aQ("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!aR(a.algorithm,"RSA-OAEP"))throw aQ("RSA-OAEP");let c=parseInt(b.slice(9),10)||1;if(parseInt(a.algorithm.hash.name.slice(4),10)!==c)throw aQ(`SHA-${c}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}if(c&&!a.usages.includes(c))throw TypeError(`CryptoKey does not support this operation, its usages must include ${c}.`)}async function aT(a,b,c,d,e){if(!(c instanceof Uint8Array))throw TypeError(aG(c,"Uint8Array"));let f=parseInt(a.slice(1,4),10),g=await crypto.subtle.importKey("raw",c.subarray(f>>3),"AES-CBC",!1,["encrypt"]),h=await crypto.subtle.importKey("raw",c.subarray(0,f>>3),{hash:`SHA-${f<<1}`,name:"HMAC"},!1,["sign"]),i=new Uint8Array(await crypto.subtle.encrypt({iv:d,name:"AES-CBC"},g,b)),j=aj(e,d,i,al(e.length<<3));return{ciphertext:i,tag:new Uint8Array((await crypto.subtle.sign("HMAC",h,j)).slice(0,f>>3)),iv:d}}async function aU(a,b,c,d,e){let f;c instanceof Uint8Array?f=await crypto.subtle.importKey("raw",c,"AES-GCM",!1,["encrypt"]):(aS(c,a,"encrypt"),f=c);let g=new Uint8Array(await crypto.subtle.encrypt({additionalData:e,iv:d,name:"AES-GCM",tagLength:128},f,b)),h=g.slice(-16);return{ciphertext:g.slice(0,-16),tag:h,iv:d}}let aV=async(a,b,c,d,e)=>{if(!aA(c)&&!(c instanceof Uint8Array))throw TypeError(aG(c,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(d)aO(a,d);else d=crypto.getRandomValues(new Uint8Array(aN(a)>>3));switch(a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return c instanceof Uint8Array&&aP(c,parseInt(a.slice(-3),10)),aT(a,b,c,d,e);case"A128GCM":case"A192GCM":case"A256GCM":return c instanceof Uint8Array&&aP(c,parseInt(a.slice(1,4),10)),aU(a,b,c,d,e);default:throw new at("Unsupported JWE Content Encryption Algorithm")}};function aW(a,b){if(a.algorithm.length!==parseInt(b.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${b}`)}function aX(a,b,c){return a instanceof Uint8Array?crypto.subtle.importKey("raw",a,"AES-KW",!0,[c]):(aS(a,b,c),a)}async function aY(a,b,c){let d=await aX(b,a,"wrapKey");aW(d,a);let e=await crypto.subtle.importKey("raw",c,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",e,d,"AES-KW"))}async function aZ(a,b,c){let d=await aX(b,a,"unwrapKey");aW(d,a);let e=await crypto.subtle.unwrapKey("raw",c,d,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",e))}function a$(a){return aj(am(a.length),a)}async function a_(a,b,c){let d=Math.ceil((b>>3)/32),e=new Uint8Array(32*d);for(let b=0;b<d;b++){let d=new Uint8Array(4+a.length+c.length);d.set(am(b+1)),d.set(a,4),d.set(c,4+a.length),e.set(await ag("sha256",d),32*b)}return e.slice(0,b>>3)}async function a0(a,b,c,d,e=new Uint8Array(0),f=new Uint8Array(0)){let g;aS(a,"ECDH"),aS(b,"ECDH","deriveBits");let h=aj(a$(ah.encode(c)),a$(e),a$(f),am(d));return g="X25519"===a.algorithm.name?256:Math.ceil(parseInt(a.algorithm.namedCurve.slice(-3),10)/8)<<3,a_(new Uint8Array(await crypto.subtle.deriveBits({name:a.algorithm.name,public:a},b,g)),d,h)}function a1(a){switch(a.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===a.algorithm.name}}async function a2(a,b,c,d){if(!(a instanceof Uint8Array)||a.length<8)throw new av("PBES2 Salt Input must be 8 or more octets");let e=aj(ah.encode(b),new Uint8Array([0]),a),f=parseInt(b.slice(13,16),10),g={hash:`SHA-${b.slice(8,11)}`,iterations:c,name:"PBKDF2",salt:e},h=await (d instanceof Uint8Array?crypto.subtle.importKey("raw",d,"PBKDF2",!1,["deriveBits"]):(aS(d,b,"deriveBits"),d));return new Uint8Array(await crypto.subtle.deriveBits(g,h,f))}async function a3(a,b,c,d=2048,e=crypto.getRandomValues(new Uint8Array(16))){let f=await a2(e,a,d,b);return{encryptedKey:await aY(a.slice(-6),f,c),p2c:d,p2s:ao(e)}}async function a4(a,b,c,d,e){let f=await a2(e,a,d,b);return aZ(a.slice(-6),f,c)}let a5=(a,b)=>{if(a.startsWith("RS")||a.startsWith("PS")){let{modulusLength:c}=b.algorithm;if("number"!=typeof c||c<2048)throw TypeError(`${a} requires key modulusLength to be 2048 bits or larger`)}},a6=a=>{switch(a){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new at(`alg ${a} is not supported either by JOSE or your javascript runtime`)}};async function a7(a,b,c){return aS(b,a,"encrypt"),a5(a,b),new Uint8Array(await crypto.subtle.encrypt(a6(a),b,c))}async function a8(a,b,c){return aS(b,a,"decrypt"),a5(a,b),new Uint8Array(await crypto.subtle.decrypt(a6(a),b,c))}let a9=async a=>{if(!a.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:b,keyUsages:c}=function(a){let b,c;switch(a.kty){case"RSA":switch(a.alg){case"PS256":case"PS384":case"PS512":b={name:"RSA-PSS",hash:`SHA-${a.alg.slice(-3)}`},c=a.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":b={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${a.alg.slice(-3)}`},c=a.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":b={name:"RSA-OAEP",hash:`SHA-${parseInt(a.alg.slice(-3),10)||1}`},c=a.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new at('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(a.alg){case"ES256":b={name:"ECDSA",namedCurve:"P-256"},c=a.d?["sign"]:["verify"];break;case"ES384":b={name:"ECDSA",namedCurve:"P-384"},c=a.d?["sign"]:["verify"];break;case"ES512":b={name:"ECDSA",namedCurve:"P-521"},c=a.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":b={name:"ECDH",namedCurve:a.crv},c=a.d?["deriveBits"]:[];break;default:throw new at('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(a.alg){case"Ed25519":case"EdDSA":b={name:"Ed25519"},c=a.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":b={name:a.crv},c=a.d?["deriveBits"]:[];break;default:throw new at('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new at('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:b,keyUsages:c}}(a),d={...a};return delete d.alg,delete d.use,crypto.subtle.importKey("jwk",d,b,a.ext??!a.d,a.key_ops??c)},ba=async(a,b,c,d=!1)=>{let f=(e||=new WeakMap).get(a);if(f?.[c])return f[c];let g=await a9({...b,alg:c});return d&&Object.freeze(a),f?f[c]=g:e.set(a,{[c]:g}),g},bb=async(a,b)=>{if(a instanceof Uint8Array||aA(a))return a;if(aB(a)){if("secret"===a.type)return a.export();if("toCryptoKey"in a&&"function"==typeof a.toCryptoKey)try{return((a,b)=>{let c,d=(e||=new WeakMap).get(a);if(d?.[b])return d[b];let f="public"===a.type,g=!!f;if("x25519"===a.asymmetricKeyType){switch(b){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}c=a.toCryptoKey(a.asymmetricKeyType,g,f?[]:["deriveBits"])}if("ed25519"===a.asymmetricKeyType){if("EdDSA"!==b&&"Ed25519"!==b)throw TypeError("given KeyObject instance cannot be used for this algorithm");c=a.toCryptoKey(a.asymmetricKeyType,g,[f?"verify":"sign"])}if("rsa"===a.asymmetricKeyType){let d;switch(b){case"RSA-OAEP":d="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":d="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":d="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":d="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(b.startsWith("RSA-OAEP"))return a.toCryptoKey({name:"RSA-OAEP",hash:d},g,f?["encrypt"]:["decrypt"]);c=a.toCryptoKey({name:b.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:d},g,[f?"verify":"sign"])}if("ec"===a.asymmetricKeyType){let d=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(a.asymmetricKeyDetails?.namedCurve);if(!d)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===b&&"P-256"===d&&(c=a.toCryptoKey({name:"ECDSA",namedCurve:d},g,[f?"verify":"sign"])),"ES384"===b&&"P-384"===d&&(c=a.toCryptoKey({name:"ECDSA",namedCurve:d},g,[f?"verify":"sign"])),"ES512"===b&&"P-521"===d&&(c=a.toCryptoKey({name:"ECDSA",namedCurve:d},g,[f?"verify":"sign"])),b.startsWith("ECDH-ES")&&(c=a.toCryptoKey({name:"ECDH",namedCurve:d},g,f?[]:["deriveBits"]))}if(!c)throw TypeError("given KeyObject instance cannot be used for this algorithm");return d?d[b]=c:e.set(a,{[b]:c}),c})(a,b)}catch(a){if(a instanceof TypeError)throw a}let c=a.export({format:"jwk"});return ba(a,c,b)}if(aE(a))return a.k?an(a.k):ba(a,a,b,!0);throw Error("unreachable")};function bc(a){switch(a){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new at(`Unsupported JWE Algorithm: ${a}`)}}let bd=a=>crypto.getRandomValues(new Uint8Array(bc(a)>>3));async function be(a,b){if(!(a instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(b instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let c={name:"HMAC",hash:"SHA-256"},d=await crypto.subtle.generateKey(c,!1,["sign"]),e=new Uint8Array(await crypto.subtle.sign(c,d,a)),f=new Uint8Array(await crypto.subtle.sign(c,d,b)),g=0,h=-1;for(;++h<32;)g|=e[h]^f[h];return 0===g}async function bf(a,b,c,d,e,f){let g,h;if(!(b instanceof Uint8Array))throw TypeError(aG(b,"Uint8Array"));let i=parseInt(a.slice(1,4),10),j=await crypto.subtle.importKey("raw",b.subarray(i>>3),"AES-CBC",!1,["decrypt"]),k=await crypto.subtle.importKey("raw",b.subarray(0,i>>3),{hash:`SHA-${i<<1}`,name:"HMAC"},!1,["sign"]),l=aj(f,d,c,al(f.length<<3)),m=new Uint8Array((await crypto.subtle.sign("HMAC",k,l)).slice(0,i>>3));try{g=await be(e,m)}catch{}if(!g)throw new au;try{h=new Uint8Array(await crypto.subtle.decrypt({iv:d,name:"AES-CBC"},j,c))}catch{}if(!h)throw new au;return h}async function bg(a,b,c,d,e,f){let g;b instanceof Uint8Array?g=await crypto.subtle.importKey("raw",b,"AES-GCM",!1,["decrypt"]):(aS(b,a,"decrypt"),g=b);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:f,iv:d,name:"AES-GCM",tagLength:128},g,aj(c,e)))}catch{throw new au}}let bh=async(a,b,c,d,e,f)=>{if(!aA(b)&&!(b instanceof Uint8Array))throw TypeError(aG(b,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!d)throw new av("JWE Initialization Vector missing");if(!e)throw new av("JWE Authentication Tag missing");switch(aO(a,d),a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return b instanceof Uint8Array&&aP(b,parseInt(a.slice(-3),10)),bf(a,b,c,d,e,f);case"A128GCM":case"A192GCM":case"A256GCM":return b instanceof Uint8Array&&aP(b,parseInt(a.slice(1,4),10)),bg(a,b,c,d,e,f);default:throw new at("Unsupported JWE Content Encryption Algorithm")}};async function bi(a,b,c,d){let e=a.slice(0,7),f=await aV(e,c,b,d,new Uint8Array(0));return{encryptedKey:f.ciphertext,iv:ao(f.iv),tag:ao(f.tag)}}async function bj(a,b,c,d,e){return bh(a.slice(0,7),b,c,d,e,new Uint8Array(0))}let bk=async(a,b,c,d,e={})=>{let f,g,h;switch(a){case"dir":h=c;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i;if(az(c),!a1(c))throw new at("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:j,apv:k}=e;i=e.epk?await bb(e.epk,a):(await crypto.subtle.generateKey(c.algorithm,!0,["deriveBits"])).privateKey;let{x:l,y:m,crv:n,kty:o}=await aJ(i),p=await a0(c,i,"ECDH-ES"===a?b:a,"ECDH-ES"===a?bc(b):parseInt(a.slice(-5,-2),10),j,k);if(g={epk:{x:l,crv:n,kty:o}},"EC"===o&&(g.epk.y=m),j&&(g.apu=ao(j)),k&&(g.apv=ao(k)),"ECDH-ES"===a){h=p;break}h=d||bd(b);let q=a.slice(-6);f=await aY(q,p,h);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":h=d||bd(b),az(c),f=await a7(a,c,h);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{h=d||bd(b);let{p2c:i,p2s:j}=e;({encryptedKey:f,...g}=await a3(a,c,h,i,j));break}case"A128KW":case"A192KW":case"A256KW":h=d||bd(b),f=await aY(a,c,h);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{h=d||bd(b);let{iv:i}=e;({encryptedKey:f,...g}=await bi(a,c,h,i));break}default:throw new at('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:h,encryptedKey:f,parameters:g}},bl=(...a)=>{let b,c=a.filter(Boolean);if(0===c.length||1===c.length)return!0;for(let a of c){let c=Object.keys(a);if(!b||0===b.size){b=new Set(c);continue}for(let a of c){if(b.has(a))return!1;b.add(a)}}return!0},bm=(a,b,c,d,e)=>{let f;if(void 0!==e.crit&&d?.crit===void 0)throw new a('"crit" (Critical) Header Parameter MUST be integrity protected');if(!d||void 0===d.crit)return new Set;if(!Array.isArray(d.crit)||0===d.crit.length||d.crit.some(a=>"string"!=typeof a||0===a.length))throw new a('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let g of(f=void 0!==c?new Map([...Object.entries(c),...b.entries()]):b,d.crit)){if(!f.has(g))throw new at(`Extension Header Parameter "${g}" is not recognized`);if(void 0===e[g])throw new a(`Extension Header Parameter "${g}" is missing`);if(f.get(g)&&void 0===d[g])throw new a(`Extension Header Parameter "${g}" MUST be integrity protected`)}return new Set(d.crit)},bn=a=>a?.[Symbol.toStringTag],bo=(a,b,c)=>{if(void 0!==b.use){let a;switch(c){case"sign":case"verify":a="sig";break;case"encrypt":case"decrypt":a="enc"}if(b.use!==a)throw TypeError(`Invalid key for this operation, its "use" must be "${a}" when present`)}if(void 0!==b.alg&&b.alg!==a)throw TypeError(`Invalid key for this operation, its "alg" must be "${a}" when present`);if(Array.isArray(b.key_ops)){let d;switch(!0){case"sign"===c||"verify"===c:case"dir"===a:case a.includes("CBC-HS"):d=c;break;case a.startsWith("PBES2"):d="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(a):d=!a.includes("GCM")&&a.endsWith("KW")?"encrypt"===c?"wrapKey":"unwrapKey":c;break;case"encrypt"===c&&a.startsWith("RSA"):d="wrapKey";break;case"decrypt"===c:d=a.startsWith("RSA")?"unwrapKey":"deriveBits"}if(d&&b.key_ops?.includes?.(d)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${d}" when present`)}return!0},bp=(a,b,c)=>{a.startsWith("HS")||"dir"===a||a.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(a)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(a)?((a,b,c)=>{if(!(b instanceof Uint8Array)){if(aE(b)){if(function(a){return"oct"===a.kty&&"string"==typeof a.k}(b)&&bo(a,b,c))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!aC(b))throw TypeError(aH(a,b,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==b.type)throw TypeError(`${bn(b)} instances for symmetric algorithms must be of type "secret"`)}})(a,b,c):((a,b,c)=>{if(aE(b))switch(c){case"decrypt":case"sign":if(function(a){return"oct"!==a.kty&&"string"==typeof a.d}(b)&&bo(a,b,c))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(a){return"oct"!==a.kty&&void 0===a.d}(b)&&bo(a,b,c))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!aC(b))throw TypeError(aH(a,b,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===b.type)throw TypeError(`${bn(b)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===b.type)switch(c){case"sign":throw TypeError(`${bn(b)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${bn(b)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===b.type)switch(c){case"verify":throw TypeError(`${bn(b)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${bn(b)} instances for asymmetric algorithm encryption must be of type "public"`)}})(a,b,c)};class bq{#a;#b;#c;#d;#e;#f;#g;#h;constructor(a){if(!(a instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#a=a}setKeyManagementParameters(a){if(this.#h)throw TypeError("setKeyManagementParameters can only be called once");return this.#h=a,this}setProtectedHeader(a){if(this.#b)throw TypeError("setProtectedHeader can only be called once");return this.#b=a,this}setSharedUnprotectedHeader(a){if(this.#c)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#c=a,this}setUnprotectedHeader(a){if(this.#d)throw TypeError("setUnprotectedHeader can only be called once");return this.#d=a,this}setAdditionalAuthenticatedData(a){return this.#e=a,this}setContentEncryptionKey(a){if(this.#f)throw TypeError("setContentEncryptionKey can only be called once");return this.#f=a,this}setInitializationVector(a){if(this.#g)throw TypeError("setInitializationVector can only be called once");return this.#g=a,this}async encrypt(a,b){let c,d,e,f,g;if(!this.#b&&!this.#d&&!this.#c)throw new av("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!bl(this.#b,this.#d,this.#c))throw new av("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let h={...this.#b,...this.#d,...this.#c};if(bm(av,new Map,b?.crit,this.#b,h),void 0!==h.zip)throw new at('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:i,enc:j}=h;if("string"!=typeof i||!i)throw new av('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof j||!j)throw new av('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#f&&("dir"===i||"ECDH-ES"===i))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${i}`);bp("dir"===i?j:i,a,"encrypt");{let e,f=await bb(a,i);({cek:d,encryptedKey:c,parameters:e}=await bk(i,j,f,this.#f,this.#h)),e&&(b&&aM in b?this.#d?this.#d={...this.#d,...e}:this.setUnprotectedHeader(e):this.#b?this.#b={...this.#b,...e}:this.setProtectedHeader(e))}f=this.#b?ah.encode(ao(JSON.stringify(this.#b))):ah.encode(""),this.#e?(g=ao(this.#e),e=aj(f,ah.encode("."),ah.encode(g))):e=f;let{ciphertext:k,tag:l,iv:m}=await aV(j,this.#a,d,this.#g,e),n={ciphertext:ao(k)};return m&&(n.iv=ao(m)),l&&(n.tag=ao(l)),c&&(n.encrypted_key=ao(c)),g&&(n.aad=g),this.#b&&(n.protected=ai.decode(f)),this.#c&&(n.unprotected=this.#c),this.#d&&(n.header=this.#d),n}}class br{#i;constructor(a){this.#i=new bq(a)}setContentEncryptionKey(a){return this.#i.setContentEncryptionKey(a),this}setInitializationVector(a){return this.#i.setInitializationVector(a),this}setProtectedHeader(a){return this.#i.setProtectedHeader(a),this}setKeyManagementParameters(a){return this.#i.setKeyManagementParameters(a),this}async encrypt(a,b){let c=await this.#i.encrypt(a,b);return[c.protected,c.encrypted_key,c.iv,c.ciphertext,c.tag].join(".")}}let bs=a=>Math.floor(a.getTime()/1e3),bt=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,bu=a=>{let b,c=bt.exec(a);if(!c||c[4]&&c[1])throw TypeError("Invalid time period format");let d=parseFloat(c[2]);switch(c[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":b=Math.round(d);break;case"minute":case"minutes":case"min":case"mins":case"m":b=Math.round(60*d);break;case"hour":case"hours":case"hr":case"hrs":case"h":b=Math.round(3600*d);break;case"day":case"days":case"d":b=Math.round(86400*d);break;case"week":case"weeks":case"w":b=Math.round(604800*d);break;default:b=Math.round(0x1e187e0*d)}return"-"===c[1]||"ago"===c[4]?-b:b};function bv(a,b){if(!Number.isFinite(b))throw TypeError(`Invalid ${a} input`);return b}let bw=a=>a.includes("/")?a.toLowerCase():`application/${a.toLowerCase()}`;class bx{#j;constructor(a){if(!aD(a))throw TypeError("JWT Claims Set MUST be an object");this.#j=structuredClone(a)}data(){return ah.encode(JSON.stringify(this.#j))}get iss(){return this.#j.iss}set iss(a){this.#j.iss=a}get sub(){return this.#j.sub}set sub(a){this.#j.sub=a}get aud(){return this.#j.aud}set aud(a){this.#j.aud=a}set jti(a){this.#j.jti=a}set nbf(a){"number"==typeof a?this.#j.nbf=bv("setNotBefore",a):a instanceof Date?this.#j.nbf=bv("setNotBefore",bs(a)):this.#j.nbf=bs(new Date)+bu(a)}set exp(a){"number"==typeof a?this.#j.exp=bv("setExpirationTime",a):a instanceof Date?this.#j.exp=bv("setExpirationTime",bs(a)):this.#j.exp=bs(new Date)+bu(a)}set iat(a){void 0===a?this.#j.iat=bs(new Date):a instanceof Date?this.#j.iat=bv("setIssuedAt",bs(a)):"string"==typeof a?this.#j.iat=bv("setIssuedAt",bs(new Date)+bu(a)):this.#j.iat=bv("setIssuedAt",a)}}class by{#f;#g;#h;#b;#k;#l;#m;#n;constructor(a={}){this.#n=new bx(a)}setIssuer(a){return this.#n.iss=a,this}setSubject(a){return this.#n.sub=a,this}setAudience(a){return this.#n.aud=a,this}setJti(a){return this.#n.jti=a,this}setNotBefore(a){return this.#n.nbf=a,this}setExpirationTime(a){return this.#n.exp=a,this}setIssuedAt(a){return this.#n.iat=a,this}setProtectedHeader(a){if(this.#b)throw TypeError("setProtectedHeader can only be called once");return this.#b=a,this}setKeyManagementParameters(a){if(this.#h)throw TypeError("setKeyManagementParameters can only be called once");return this.#h=a,this}setContentEncryptionKey(a){if(this.#f)throw TypeError("setContentEncryptionKey can only be called once");return this.#f=a,this}setInitializationVector(a){if(this.#g)throw TypeError("setInitializationVector can only be called once");return this.#g=a,this}replicateIssuerAsHeader(){return this.#k=!0,this}replicateSubjectAsHeader(){return this.#l=!0,this}replicateAudienceAsHeader(){return this.#m=!0,this}async encrypt(a,b){let c=new br(this.#n.data());return this.#b&&(this.#k||this.#l||this.#m)&&(this.#b={...this.#b,iss:this.#k?this.#n.iss:void 0,sub:this.#l?this.#n.sub:void 0,aud:this.#m?this.#n.aud:void 0}),c.setProtectedHeader(this.#b),this.#g&&c.setInitializationVector(this.#g),this.#f&&c.setContentEncryptionKey(this.#f),this.#h&&c.setKeyManagementParameters(this.#h),c.encrypt(a,b)}}async function bz(a,b,c){let d;if(!aD(a))throw TypeError("JWK must be an object");switch(b??=a.alg,d??=c?.extractable??a.ext,a.kty){case"oct":if("string"!=typeof a.k||!a.k)throw TypeError('missing "k" (Key Value) Parameter value');return an(a.k);case"RSA":if("oth"in a&&void 0!==a.oth)throw new at('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return a9({...a,alg:b,ext:d});default:throw new at('Unsupported "kty" (Key Type) Parameter value')}}let bA=async(a,b,c,d,e)=>{switch(a){case"dir":if(void 0!==c)throw new av("Encountered unexpected JWE Encrypted Key");return b;case"ECDH-ES":if(void 0!==c)throw new av("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let e,f;if(!aD(d.epk))throw new av('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(az(b),!a1(b))throw new at("ECDH with the provided key is not allowed or not supported by your javascript runtime");let g=await bz(d.epk,a);if(az(g),void 0!==d.apu){if("string"!=typeof d.apu)throw new av('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{e=an(d.apu)}catch{throw new av("Failed to base64url decode the apu")}}if(void 0!==d.apv){if("string"!=typeof d.apv)throw new av('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{f=an(d.apv)}catch{throw new av("Failed to base64url decode the apv")}}let h=await a0(g,b,"ECDH-ES"===a?d.enc:a,"ECDH-ES"===a?bc(d.enc):parseInt(a.slice(-5,-2),10),e,f);if("ECDH-ES"===a)return h;if(void 0===c)throw new av("JWE Encrypted Key missing");return aZ(a.slice(-6),h,c)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===c)throw new av("JWE Encrypted Key missing");return az(b),a8(a,b,c);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let f;if(void 0===c)throw new av("JWE Encrypted Key missing");if("number"!=typeof d.p2c)throw new av('JOSE Header "p2c" (PBES2 Count) missing or invalid');let g=e?.maxPBES2Count||1e4;if(d.p2c>g)throw new av('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof d.p2s)throw new av('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{f=an(d.p2s)}catch{throw new av("Failed to base64url decode the p2s")}return a4(a,b,c,d.p2c,f)}case"A128KW":case"A192KW":case"A256KW":if(void 0===c)throw new av("JWE Encrypted Key missing");return aZ(a,b,c);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let e,f;if(void 0===c)throw new av("JWE Encrypted Key missing");if("string"!=typeof d.iv)throw new av('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof d.tag)throw new av('JOSE Header "tag" (Authentication Tag) missing or invalid');try{e=an(d.iv)}catch{throw new av("Failed to base64url decode the iv")}try{f=an(d.tag)}catch{throw new av("Failed to base64url decode the tag")}return bj(a,b,c,e,f)}default:throw new at('Invalid or unsupported "alg" (JWE Algorithm) header value')}},bB=(a,b)=>{if(void 0!==b&&(!Array.isArray(b)||b.some(a=>"string"!=typeof a)))throw TypeError(`"${a}" option must be an array of strings`);if(b)return new Set(b)};async function bC(a,b,c){let d,e,f,g,h,i,j;if(!aD(a))throw new av("Flattened JWE must be an object");if(void 0===a.protected&&void 0===a.header&&void 0===a.unprotected)throw new av("JOSE Header missing");if(void 0!==a.iv&&"string"!=typeof a.iv)throw new av("JWE Initialization Vector incorrect type");if("string"!=typeof a.ciphertext)throw new av("JWE Ciphertext missing or incorrect type");if(void 0!==a.tag&&"string"!=typeof a.tag)throw new av("JWE Authentication Tag incorrect type");if(void 0!==a.protected&&"string"!=typeof a.protected)throw new av("JWE Protected Header incorrect type");if(void 0!==a.encrypted_key&&"string"!=typeof a.encrypted_key)throw new av("JWE Encrypted Key incorrect type");if(void 0!==a.aad&&"string"!=typeof a.aad)throw new av("JWE AAD incorrect type");if(void 0!==a.header&&!aD(a.header))throw new av("JWE Shared Unprotected Header incorrect type");if(void 0!==a.unprotected&&!aD(a.unprotected))throw new av("JWE Per-Recipient Unprotected Header incorrect type");if(a.protected)try{let b=an(a.protected);d=JSON.parse(ai.decode(b))}catch{throw new av("JWE Protected Header is invalid")}if(!bl(d,a.header,a.unprotected))throw new av("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let k={...d,...a.header,...a.unprotected};if(bm(av,new Map,c?.crit,d,k),void 0!==k.zip)throw new at('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:l,enc:m}=k;if("string"!=typeof l||!l)throw new av("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof m||!m)throw new av("missing JWE Encryption Algorithm (enc) in JWE Header");let n=c&&bB("keyManagementAlgorithms",c.keyManagementAlgorithms),o=c&&bB("contentEncryptionAlgorithms",c.contentEncryptionAlgorithms);if(n&&!n.has(l)||!n&&l.startsWith("PBES2"))throw new as('"alg" (Algorithm) Header Parameter value not allowed');if(o&&!o.has(m))throw new as('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==a.encrypted_key)try{e=an(a.encrypted_key)}catch{throw new av("Failed to base64url decode the encrypted_key")}let p=!1;"function"==typeof b&&(b=await b(d,a),p=!0),bp("dir"===l?m:l,b,"decrypt");let q=await bb(b,l);try{f=await bA(l,q,e,k,c)}catch(a){if(a instanceof TypeError||a instanceof av||a instanceof at)throw a;f=bd(m)}if(void 0!==a.iv)try{g=an(a.iv)}catch{throw new av("Failed to base64url decode the iv")}if(void 0!==a.tag)try{h=an(a.tag)}catch{throw new av("Failed to base64url decode the tag")}let r=ah.encode(a.protected??"");i=void 0!==a.aad?aj(r,ah.encode("."),ah.encode(a.aad)):r;try{j=an(a.ciphertext)}catch{throw new av("Failed to base64url decode the ciphertext")}let s={plaintext:await bh(m,f,j,g,h,i)};if(void 0!==a.protected&&(s.protectedHeader=d),void 0!==a.aad)try{s.additionalAuthenticatedData=an(a.aad)}catch{throw new av("Failed to base64url decode the aad")}return(void 0!==a.unprotected&&(s.sharedUnprotectedHeader=a.unprotected),void 0!==a.header&&(s.unprotectedHeader=a.header),p)?{...s,key:q}:s}async function bD(a,b,c){if(a instanceof Uint8Array&&(a=ai.decode(a)),"string"!=typeof a)throw new av("Compact JWE must be a string or Uint8Array");let{0:d,1:e,2:f,3:g,4:h,length:i}=a.split(".");if(5!==i)throw new av("Invalid Compact JWE");let j=await bC({ciphertext:g,iv:f||void 0,protected:d,tag:h||void 0,encrypted_key:e||void 0},b,c),k={plaintext:j.plaintext,protectedHeader:j.protectedHeader};return"function"==typeof b?{...k,key:j.key}:k}async function bE(a,b,c){let d=await bD(a,b,c),e=function(a,b,c={}){var d,e;let f,g;try{f=JSON.parse(ai.decode(b))}catch{}if(!aD(f))throw new aw("JWT Claims Set must be a top-level JSON object");let{typ:h}=c;if(h&&("string"!=typeof a.typ||bw(a.typ)!==bw(h)))throw new aq('unexpected "typ" JWT header value',f,"typ","check_failed");let{requiredClaims:i=[],issuer:j,subject:k,audience:l,maxTokenAge:m}=c,n=[...i];for(let a of(void 0!==m&&n.push("iat"),void 0!==l&&n.push("aud"),void 0!==k&&n.push("sub"),void 0!==j&&n.push("iss"),new Set(n.reverse())))if(!(a in f))throw new aq(`missing required "${a}" claim`,f,a,"missing");if(j&&!(Array.isArray(j)?j:[j]).includes(f.iss))throw new aq('unexpected "iss" claim value',f,"iss","check_failed");if(k&&f.sub!==k)throw new aq('unexpected "sub" claim value',f,"sub","check_failed");if(l&&(d=f.aud,e="string"==typeof l?[l]:l,"string"==typeof d?!e.includes(d):!(Array.isArray(d)&&e.some(Set.prototype.has.bind(new Set(d))))))throw new aq('unexpected "aud" claim value',f,"aud","check_failed");switch(typeof c.clockTolerance){case"string":g=bu(c.clockTolerance);break;case"number":g=c.clockTolerance;break;case"undefined":g=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:o}=c,p=bs(o||new Date);if((void 0!==f.iat||m)&&"number"!=typeof f.iat)throw new aq('"iat" claim must be a number',f,"iat","invalid");if(void 0!==f.nbf){if("number"!=typeof f.nbf)throw new aq('"nbf" claim must be a number',f,"nbf","invalid");if(f.nbf>p+g)throw new aq('"nbf" claim timestamp check failed',f,"nbf","check_failed")}if(void 0!==f.exp){if("number"!=typeof f.exp)throw new aq('"exp" claim must be a number',f,"exp","invalid");if(f.exp<=p-g)throw new ar('"exp" claim timestamp check failed',f,"exp","check_failed")}if(m){let a=p-f.iat;if(a-g>("number"==typeof m?m:bu(m)))throw new ar('"iat" claim timestamp check failed (too far in the past)',f,"iat","check_failed");if(a<0-g)throw new aq('"iat" claim timestamp check failed (it should be in the past)',f,"iat","check_failed")}return f}(d.protectedHeader,d.plaintext,c),{protectedHeader:f}=d;if(void 0!==f.iss&&f.iss!==e.iss)throw new aq('replicated "iss" claim header parameter mismatch',e,"iss","mismatch");if(void 0!==f.sub&&f.sub!==e.sub)throw new aq('replicated "sub" claim header parameter mismatch',e,"sub","mismatch");if(void 0!==f.aud&&JSON.stringify(f.aud)!==JSON.stringify(e.aud))throw new aq('replicated "aud" claim header parameter mismatch',e,"aud","mismatch");let g={payload:e,protectedHeader:f};return"function"==typeof b?{...g,key:d.key}:g}let bF=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,bG=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,bH=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,bI=/^[\u0020-\u003A\u003D-\u007E]*$/,bJ=Object.prototype.toString,bK=(()=>{let a=function(){};return a.prototype=Object.create(null),a})();function bL(a,b){let c=new bK,d=a.length;if(d<2)return c;let e=b?.decode||bP,f=0;do{let b=a.indexOf("=",f);if(-1===b)break;let g=a.indexOf(";",f),h=-1===g?d:g;if(b>h){f=a.lastIndexOf(";",b-1)+1;continue}let i=bM(a,f,b),j=bN(a,b,i),k=a.slice(i,j);if(void 0===c[k]){let d=bM(a,b+1,h),f=bN(a,h,d),g=e(a.slice(d,f));c[k]=g}f=h+1}while(f<d);return c}function bM(a,b,c){do{let c=a.charCodeAt(b);if(32!==c&&9!==c)return b}while(++b<c);return c}function bN(a,b,c){for(;b>c;){let c=a.charCodeAt(--b);if(32!==c&&9!==c)return b+1}return c}function bO(a,b,c){let d=c?.encode||encodeURIComponent;if(!bF.test(a))throw TypeError(`argument name is invalid: ${a}`);let e=d(b);if(!bG.test(e))throw TypeError(`argument val is invalid: ${b}`);let f=a+"="+e;if(!c)return f;if(void 0!==c.maxAge){if(!Number.isInteger(c.maxAge))throw TypeError(`option maxAge is invalid: ${c.maxAge}`);f+="; Max-Age="+c.maxAge}if(c.domain){if(!bH.test(c.domain))throw TypeError(`option domain is invalid: ${c.domain}`);f+="; Domain="+c.domain}if(c.path){if(!bI.test(c.path))throw TypeError(`option path is invalid: ${c.path}`);f+="; Path="+c.path}if(c.expires){var g;if(g=c.expires,"[object Date]"!==bJ.call(g)||!Number.isFinite(c.expires.valueOf()))throw TypeError(`option expires is invalid: ${c.expires}`);f+="; Expires="+c.expires.toUTCString()}if(c.httpOnly&&(f+="; HttpOnly"),c.secure&&(f+="; Secure"),c.partitioned&&(f+="; Partitioned"),c.priority)switch("string"==typeof c.priority?c.priority.toLowerCase():void 0){case"low":f+="; Priority=Low";break;case"medium":f+="; Priority=Medium";break;case"high":f+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${c.priority}`)}if(c.sameSite)switch("string"==typeof c.sameSite?c.sameSite.toLowerCase():c.sameSite){case!0:case"strict":f+="; SameSite=Strict";break;case"lax":f+="; SameSite=Lax";break;case"none":f+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${c.sameSite}`)}return f}function bP(a){if(-1===a.indexOf("%"))return a;try{return decodeURIComponent(a)}catch(b){return a}}let{q:bQ}=i,bR="A256CBC-HS512";async function bS(a){let{token:b={},secret:c,maxAge:d=2592e3,salt:e}=a,f=Array.isArray(c)?c:[c],g=await bU(bR,f[0],e),h=await aL({kty:"oct",k:ao(g)},`sha${g.byteLength<<3}`);return await new by(b).setProtectedHeader({alg:"dir",enc:bR,kid:h}).setIssuedAt().setExpirationTime((Date.now()/1e3|0)+d).setJti(crypto.randomUUID()).encrypt(g)}async function bT(a){let{token:b,secret:c,salt:d}=a,e=Array.isArray(c)?c:[c];if(!b)return null;let{payload:f}=await bE(b,async({kid:a,enc:b})=>{for(let c of e){let e=await bU(b,c,d);if(void 0===a||a===await aL({kty:"oct",k:ao(e)},`sha${e.byteLength<<3}`))return e}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[bR,"A256GCM"]});return f}async function bU(a,b,c){let d;switch(a){case"A256CBC-HS512":d=64;break;case"A256GCM":d=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await af("sha256",b,c,`Auth.js Generated Encryption Key (${c})`,d)}async function bV({options:a,paramValue:b,cookieValue:c}){let{url:d,callbacks:e}=a,f=d.origin;return b?f=await e.redirect({url:b,baseUrl:d.origin}):c&&(f=await e.redirect({url:c,baseUrl:d.origin})),{callbackUrl:f,callbackUrlCookie:f!==c?f:void 0}}let bW="\x1b[31m",bX="\x1b[0m",bY={error(a){let b=a instanceof n?a.type:a.name;if(console.error(`${bW}[auth][error]${bX} ${b}: ${a.message}`),a.cause&&"object"==typeof a.cause&&"err"in a.cause&&a.cause.err instanceof Error){let{err:b,...c}=a.cause;console.error(`${bW}[auth][cause]${bX}:`,b.stack),c&&console.error(`${bW}[auth][details]${bX}:`,JSON.stringify(c,null,2))}else a.stack&&console.error(a.stack.replace(/.*/,"").substring(1))},warn(a){console.warn(`\x1b[33m[auth][warn][${a}]${bX}`,"Read more: https://warnings.authjs.dev")},debug(a,b){console.log(`\x1b[90m[auth][debug]:${bX} ${a}`,JSON.stringify(b,null,2))}};function bZ(a){let b={...bY};return a.debug||(b.debug=()=>{}),a.logger?.error&&(b.error=a.logger.error),a.logger?.warn&&(b.warn=a.logger.warn),a.logger?.debug&&(b.debug=a.logger.debug),a.logger??(a.logger=b),b}let b$=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:b_,l:b0}=i;async function b1(a){if(!("body"in a)||!a.body||"POST"!==a.method)return;let b=a.headers.get("content-type");return b?.includes("application/json")?await a.json():b?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await a.text())):void 0}async function b2(a,b){try{if("GET"!==a.method&&"POST"!==a.method)throw new K("Only GET and POST requests are supported");b.basePath??(b.basePath="/auth");let c=new URL(a.url),{action:d,providerId:e}=function(a,b){let c=a.match(RegExp(`^${b}(.+)`));if(null===c)throw new K(`Cannot parse action at ${a}`);let d=c.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==d.length&&2!==d.length)throw new K(`Cannot parse action at ${a}`);let[e,f]=d;if(!b$.includes(e)||f&&!["signin","callback","webauthn-options"].includes(e))throw new K(`Cannot parse action at ${a}`);return{action:e,providerId:"undefined"==f?void 0:f}}(c.pathname,b.basePath);return{url:c,action:d,providerId:e,method:a.method,headers:Object.fromEntries(a.headers),body:a.body?await b1(a):void 0,cookies:b_(a.headers.get("cookie")??"")??{},error:c.searchParams.get("error")??void 0,query:Object.fromEntries(c.searchParams)}}catch(d){let c=bZ(b);c.error(d),c.debug("request",a)}}function b3(a){let b=new Headers(a.headers);a.cookies?.forEach(a=>{let{name:c,value:d,options:e}=a,f=b0(c,d,e);b.has("Set-Cookie")?b.append("Set-Cookie",f):b.set("Set-Cookie",f)});let c=a.body;"application/json"===b.get("content-type")?c=JSON.stringify(a.body):"application/x-www-form-urlencoded"===b.get("content-type")&&(c=new URLSearchParams(a.body).toString());let d=new Response(c,{headers:b,status:a.redirect?302:a.status??200});return a.redirect&&d.headers.set("Location",a.redirect),d}async function b4(a){let b=new TextEncoder().encode(a);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",b))).map(a=>a.toString(16).padStart(2,"0")).join("").toString()}function b5(a){return Array.from(crypto.getRandomValues(new Uint8Array(a))).reduce((a,b)=>a+("0"+b.toString(16)).slice(-2),"")}async function b6({options:a,cookieValue:b,isPost:c,bodyValue:d}){if(b){let[e,f]=b.split("|");if(f===await b4(`${e}${a.secret}`))return{csrfTokenVerified:c&&e===d,csrfToken:e}}let e=b5(32),f=await b4(`${e}${a.secret}`);return{cookie:`${e}|${f}`,csrfToken:e}}function b7(a,b){if(!b)throw new P(`CSRF token was missing during an action ${a}`)}function b8(a){return null!==a&&"object"==typeof a}function b9(a,...b){if(!b.length)return a;let c=b.shift();if(b8(a)&&b8(c))for(let b in c)b8(c[b])?(b8(a[b])||(a[b]=Array.isArray(c[b])?[]:{}),b9(a[b],c[b])):void 0!==c[b]&&(a[b]=c[b]);return b9(a,...b)}let ca=Symbol("skip-csrf-check"),cb=Symbol("return-type-raw"),cc=Symbol("custom-fetch"),cd=Symbol("conform-internal"),ce=a=>cg({id:a.sub??a.id??crypto.randomUUID(),name:a.name??a.nickname??a.preferred_username,email:a.email,image:a.picture}),cf=a=>cg({access_token:a.access_token,id_token:a.id_token,refresh_token:a.refresh_token,expires_at:a.expires_at,scope:a.scope,token_type:a.token_type,session_state:a.session_state});function cg(a){let b={};for(let[c,d]of Object.entries(a))void 0!==d&&(b[c]=d);return b}function ch(a,b){if(!a&&b)return;if("string"==typeof a)return{url:new URL(a)};let c=new URL(a?.url??"https://authjs.dev");if(a?.params!=null)for(let[b,d]of Object.entries(a.params))"claims"===b&&(d=JSON.stringify(d)),c.searchParams.set(b,String(d));return{url:c,request:a?.request,conform:a?.conform,...a?.clientPrivateKey?{clientPrivateKey:a?.clientPrivateKey}:null}}let ci={signIn:()=>!0,redirect:({url:a,baseUrl:b})=>a.startsWith("/")?`${b}${a}`:new URL(a).origin===b?a:b,session:({session:a})=>({user:{name:a.user?.name,email:a.user?.email,image:a.user?.image},expires:a.expires?.toISOString?.()??a.expires}),jwt:({token:a})=>a};async function cj({authOptions:a,providerId:b,action:c,url:d,cookies:e,callbackUrl:f,csrfToken:g,csrfDisabled:h,isPost:i}){var j,k;let m=bZ(a),{providers:n,provider:o}=function(a){let{providerId:b,config:c}=a,d=new URL(c.basePath??"/auth",a.url.origin),e=c.providers.map(a=>{let b="function"==typeof a?a():a,{options:e,...f}=b,g=e?.id??f.id,h=b9(f,e,{signinUrl:`${d}/signin/${g}`,callbackUrl:`${d}/callback/${g}`});if("oauth"===b.type||"oidc"===b.type){h.redirectProxyUrl??(h.redirectProxyUrl=e?.redirectProxyUrl??c.redirectProxyUrl);let a=function(a){a.issuer&&(a.wellKnown??(a.wellKnown=`${a.issuer}/.well-known/openid-configuration`));let b=ch(a.authorization,a.issuer);b&&!b.url?.searchParams.has("scope")&&b.url.searchParams.set("scope","openid profile email");let c=ch(a.token,a.issuer),d=ch(a.userinfo,a.issuer),e=a.checks??["pkce"];return a.redirectProxyUrl&&(e.includes("state")||e.push("state"),a.redirectProxyUrl=`${a.redirectProxyUrl}/callback/${a.id}`),{...a,authorization:b,token:c,checks:e,userinfo:d,profile:a.profile??ce,account:a.account??cf}}(h);return a.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete a.redirectProxyUrl,a[cc]??(a[cc]=e?.[cc]),a}return h}),f=e.find(({id:a})=>a===b);if(b&&!f){let a=e.map(a=>a.id).join(", ");throw Error(`Provider with id "${b}" not found. Available providers: [${a}].`)}return{providers:e,provider:f}}({url:d,providerId:b,config:a}),q=!1;if((o?.type==="oauth"||o?.type==="oidc")&&o.redirectProxyUrl)try{q=new URL(o.redirectProxyUrl).origin===d.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${o.redirectProxyUrl}`)}let r={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...a,url:d,action:c,provider:o,cookies:b9(l(a.useSecureCookies??"https:"===d.protocol),a.cookies),providers:n,session:{strategy:a.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...a.session},jwt:{secret:a.secret,maxAge:a.session?.maxAge??2592e3,encode:bS,decode:bT,...a.jwt},events:(j=a.events??{},k=m,Object.keys(j).reduce((a,b)=>(a[b]=async(...a)=>{try{let c=j[b];return await c(...a)}catch(a){k.error(new t(a))}},a),{})),adapter:function(a,b){if(a)return Object.keys(a).reduce((c,d)=>(c[d]=async(...c)=>{try{b.debug(`adapter_${d}`,{args:c});let e=a[d];return await e(...c)}catch(c){let a=new p(c);throw b.error(a),a}},c),{})}(a.adapter,m),callbacks:{...ci,...a.callbacks},logger:m,callbackUrl:d.origin,isOnRedirectProxy:q,experimental:{...a.experimental}},s=[];if(h)r.csrfTokenVerified=!0;else{let{csrfToken:a,cookie:b,csrfTokenVerified:c}=await b6({options:r,cookieValue:e?.[r.cookies.csrfToken.name],isPost:i,bodyValue:g});r.csrfToken=a,r.csrfTokenVerified=c,b&&s.push({name:r.cookies.csrfToken.name,value:b,options:r.cookies.csrfToken.options})}let{callbackUrl:u,callbackUrlCookie:v}=await bV({options:r,cookieValue:e?.[r.cookies.callbackUrl.name],paramValue:f});return r.callbackUrl=u,v&&s.push({name:r.cookies.callbackUrl.name,value:v,options:r.cookies.callbackUrl.options}),{options:r,cookies:s}}var ck,cl,cm,cn,co,cp,cq,cr,cs,ct,cu,cv,cw,cx,cy,cz,cA={},cB=[],cC=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,cD=Array.isArray;function cE(a,b){for(var c in b)a[c]=b[c];return a}function cF(a){a&&a.parentNode&&a.parentNode.removeChild(a)}function cG(a,b,c,d,e){var f={type:a,props:b,key:c,ref:d,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==e?++cs:e,__i:-1,__u:0};return null==e&&null!=cr.vnode&&cr.vnode(f),f}function cH(a){return a.children}function cI(a,b){this.props=a,this.context=b}function cJ(a,b){if(null==b)return a.__?cJ(a.__,a.__i+1):null;for(var c;b<a.__k.length;b++)if(null!=(c=a.__k[b])&&null!=c.__e)return c.__e;return"function"==typeof a.type?cJ(a):null}function cK(a){(!a.__d&&(a.__d=!0)&&ct.push(a)&&!cL.__r++||cu!==cr.debounceRendering)&&((cu=cr.debounceRendering)||cv)(cL)}function cL(){var a,b,c,d,e,f,g,h;for(ct.sort(cw);a=ct.shift();)a.__d&&(b=ct.length,d=void 0,f=(e=(c=a).__v).__e,g=[],h=[],c.__P&&((d=cE({},e)).__v=e.__v+1,cr.vnode&&cr.vnode(d),cQ(c.__P,d,e,c.__n,c.__P.namespaceURI,32&e.__u?[f]:null,g,null==f?cJ(e):f,!!(32&e.__u),h),d.__v=e.__v,d.__.__k[d.__i]=d,function(a,b,c){b.__d=void 0;for(var d=0;d<c.length;d++)cR(c[d],c[++d],c[++d]);cr.__c&&cr.__c(b,a),a.some(function(b){try{a=b.__h,b.__h=[],a.some(function(a){a.call(b)})}catch(a){cr.__e(a,b.__v)}})}(g,d,h),d.__e!=f&&function a(b){var c,d;if(null!=(b=b.__)&&null!=b.__c){for(b.__e=b.__c.base=null,c=0;c<b.__k.length;c++)if(null!=(d=b.__k[c])&&null!=d.__e){b.__e=b.__c.base=d.__e;break}return a(b)}}(d)),ct.length>b&&ct.sort(cw));cL.__r=0}function cM(a,b,c,d,e,f,g,h,i,j,k){var l,m,n,o,p,q=d&&d.__k||cB,r=b.length;for(c.__d=i,function(a,b,c){var d,e,f,g,h,i=b.length,j=c.length,k=j,l=0;for(a.__k=[],d=0;d<i;d++)null!=(e=b[d])&&"boolean"!=typeof e&&"function"!=typeof e?(g=d+l,(e=a.__k[d]="string"==typeof e||"number"==typeof e||"bigint"==typeof e||e.constructor==String?cG(null,e,null,null,null):cD(e)?cG(cH,{children:e},null,null,null):void 0===e.constructor&&e.__b>0?cG(e.type,e.props,e.key,e.ref?e.ref:null,e.__v):e).__=a,e.__b=a.__b+1,f=null,-1!==(h=e.__i=function(a,b,c,d){var e=a.key,f=a.type,g=c-1,h=c+1,i=b[c];if(null===i||i&&e==i.key&&f===i.type&&0==(131072&i.__u))return c;if(d>+(null!=i&&0==(131072&i.__u)))for(;g>=0||h<b.length;){if(g>=0){if((i=b[g])&&0==(131072&i.__u)&&e==i.key&&f===i.type)return g;g--}if(h<b.length){if((i=b[h])&&0==(131072&i.__u)&&e==i.key&&f===i.type)return h;h++}}return -1}(e,c,g,k))&&(k--,(f=c[h])&&(f.__u|=131072)),null==f||null===f.__v?(-1==h&&l--,"function"!=typeof e.type&&(e.__u|=65536)):h!==g&&(h==g-1?l--:h==g+1?l++:(h>g?l--:l++,e.__u|=65536))):e=a.__k[d]=null;if(k)for(d=0;d<j;d++)null!=(f=c[d])&&0==(131072&f.__u)&&(f.__e==a.__d&&(a.__d=cJ(f)),function a(b,c,d){var e,f;if(cr.unmount&&cr.unmount(b),(e=b.ref)&&(e.current&&e.current!==b.__e||cR(e,null,c)),null!=(e=b.__c)){if(e.componentWillUnmount)try{e.componentWillUnmount()}catch(a){cr.__e(a,c)}e.base=e.__P=null}if(e=b.__k)for(f=0;f<e.length;f++)e[f]&&a(e[f],c,d||"function"!=typeof b.type);d||cF(b.__e),b.__c=b.__=b.__e=b.__d=void 0}(f,f))}(c,b,q),i=c.__d,l=0;l<r;l++)null!=(n=c.__k[l])&&(m=-1===n.__i?cA:q[n.__i]||cA,n.__i=l,cQ(a,n,m,e,f,g,h,i,j,k),o=n.__e,n.ref&&m.ref!=n.ref&&(m.ref&&cR(m.ref,null,n),k.push(n.ref,n.__c||o,n)),null==p&&null!=o&&(p=o),65536&n.__u||m.__k===n.__k?i=function a(b,c,d){var e,f;if("function"==typeof b.type){for(e=b.__k,f=0;e&&f<e.length;f++)e[f]&&(e[f].__=b,c=a(e[f],c,d));return c}b.__e!=c&&(c&&b.type&&!d.contains(c)&&(c=cJ(b)),d.insertBefore(b.__e,c||null),c=b.__e);do c=c&&c.nextSibling;while(null!=c&&8===c.nodeType);return c}(n,i,a):"function"==typeof n.type&&void 0!==n.__d?i=n.__d:o&&(i=o.nextSibling),n.__d=void 0,n.__u&=-196609);c.__d=i,c.__e=p}function cN(a,b,c){"-"===b[0]?a.setProperty(b,null==c?"":c):a[b]=null==c?"":"number"!=typeof c||cC.test(b)?c:c+"px"}function cO(a,b,c,d,e){var f;a:if("style"===b)if("string"==typeof c)a.style.cssText=c;else{if("string"==typeof d&&(a.style.cssText=d=""),d)for(b in d)c&&b in c||cN(a.style,b,"");if(c)for(b in c)d&&c[b]===d[b]||cN(a.style,b,c[b])}else if("o"===b[0]&&"n"===b[1])f=b!==(b=b.replace(/(PointerCapture)$|Capture$/i,"$1")),b=b.toLowerCase()in a||"onFocusOut"===b||"onFocusIn"===b?b.toLowerCase().slice(2):b.slice(2),a.l||(a.l={}),a.l[b+f]=c,c?d?c.u=d.u:(c.u=cx,a.addEventListener(b,f?cz:cy,f)):a.removeEventListener(b,f?cz:cy,f);else{if("http://www.w3.org/2000/svg"==e)b=b.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=b&&"height"!=b&&"href"!=b&&"list"!=b&&"form"!=b&&"tabIndex"!=b&&"download"!=b&&"rowSpan"!=b&&"colSpan"!=b&&"role"!=b&&"popover"!=b&&b in a)try{a[b]=null==c?"":c;break a}catch(a){}"function"==typeof c||(null==c||!1===c&&"-"!==b[4]?a.removeAttribute(b):a.setAttribute(b,"popover"==b&&1==c?"":c))}}function cP(a){return function(b){if(this.l){var c=this.l[b.type+a];if(null==b.t)b.t=cx++;else if(b.t<c.u)return;return c(cr.event?cr.event(b):b)}}}function cQ(a,b,c,d,e,f,g,h,i,j){var k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A=b.type;if(void 0!==b.constructor)return null;128&c.__u&&(i=!!(32&c.__u),f=[h=b.__e=c.__e]),(k=cr.__b)&&k(b);a:if("function"==typeof A)try{if(r=b.props,s="prototype"in A&&A.prototype.render,t=(k=A.contextType)&&d[k.__c],u=k?t?t.props.value:k.__:d,c.__c?q=(l=b.__c=c.__c).__=l.__E:(s?b.__c=l=new A(r,u):(b.__c=l=new cI(r,u),l.constructor=A,l.render=cS),t&&t.sub(l),l.props=r,l.state||(l.state={}),l.context=u,l.__n=d,m=l.__d=!0,l.__h=[],l._sb=[]),s&&null==l.__s&&(l.__s=l.state),s&&null!=A.getDerivedStateFromProps&&(l.__s==l.state&&(l.__s=cE({},l.__s)),cE(l.__s,A.getDerivedStateFromProps(r,l.__s))),n=l.props,o=l.state,l.__v=b,m)s&&null==A.getDerivedStateFromProps&&null!=l.componentWillMount&&l.componentWillMount(),s&&null!=l.componentDidMount&&l.__h.push(l.componentDidMount);else{if(s&&null==A.getDerivedStateFromProps&&r!==n&&null!=l.componentWillReceiveProps&&l.componentWillReceiveProps(r,u),!l.__e&&(null!=l.shouldComponentUpdate&&!1===l.shouldComponentUpdate(r,l.__s,u)||b.__v===c.__v)){for(b.__v!==c.__v&&(l.props=r,l.state=l.__s,l.__d=!1),b.__e=c.__e,b.__k=c.__k,b.__k.some(function(a){a&&(a.__=b)}),v=0;v<l._sb.length;v++)l.__h.push(l._sb[v]);l._sb=[],l.__h.length&&g.push(l);break a}null!=l.componentWillUpdate&&l.componentWillUpdate(r,l.__s,u),s&&null!=l.componentDidUpdate&&l.__h.push(function(){l.componentDidUpdate(n,o,p)})}if(l.context=u,l.props=r,l.__P=a,l.__e=!1,w=cr.__r,x=0,s){for(l.state=l.__s,l.__d=!1,w&&w(b),k=l.render(l.props,l.state,l.context),y=0;y<l._sb.length;y++)l.__h.push(l._sb[y]);l._sb=[]}else do l.__d=!1,w&&w(b),k=l.render(l.props,l.state,l.context),l.state=l.__s;while(l.__d&&++x<25);l.state=l.__s,null!=l.getChildContext&&(d=cE(cE({},d),l.getChildContext())),s&&!m&&null!=l.getSnapshotBeforeUpdate&&(p=l.getSnapshotBeforeUpdate(n,o)),cM(a,cD(z=null!=k&&k.type===cH&&null==k.key?k.props.children:k)?z:[z],b,c,d,e,f,g,h,i,j),l.base=b.__e,b.__u&=-161,l.__h.length&&g.push(l),q&&(l.__E=l.__=null)}catch(a){if(b.__v=null,i||null!=f){for(b.__u|=i?160:128;h&&8===h.nodeType&&h.nextSibling;)h=h.nextSibling;f[f.indexOf(h)]=null,b.__e=h}else b.__e=c.__e,b.__k=c.__k;cr.__e(a,b,c)}else null==f&&b.__v===c.__v?(b.__k=c.__k,b.__e=c.__e):b.__e=function(a,b,c,d,e,f,g,h,i){var j,k,l,m,n,o,p,q=c.props,r=b.props,s=b.type;if("svg"===s?e="http://www.w3.org/2000/svg":"math"===s?e="http://www.w3.org/1998/Math/MathML":e||(e="http://www.w3.org/1999/xhtml"),null!=f){for(j=0;j<f.length;j++)if((n=f[j])&&"setAttribute"in n==!!s&&(s?n.localName===s:3===n.nodeType)){a=n,f[j]=null;break}}if(null==a){if(null===s)return document.createTextNode(r);a=document.createElementNS(e,s,r.is&&r),h&&(cr.__m&&cr.__m(b,f),h=!1),f=null}if(null===s)q===r||h&&a.data===r||(a.data=r);else{if(f=f&&cq.call(a.childNodes),q=c.props||cA,!h&&null!=f)for(q={},j=0;j<a.attributes.length;j++)q[(n=a.attributes[j]).name]=n.value;for(j in q)if(n=q[j],"children"==j);else if("dangerouslySetInnerHTML"==j)l=n;else if(!(j in r)){if("value"==j&&"defaultValue"in r||"checked"==j&&"defaultChecked"in r)continue;cO(a,j,null,n,e)}for(j in r)n=r[j],"children"==j?m=n:"dangerouslySetInnerHTML"==j?k=n:"value"==j?o=n:"checked"==j?p=n:h&&"function"!=typeof n||q[j]===n||cO(a,j,n,q[j],e);if(k)h||l&&(k.__html===l.__html||k.__html===a.innerHTML)||(a.innerHTML=k.__html),b.__k=[];else if(l&&(a.innerHTML=""),cM(a,cD(m)?m:[m],b,c,d,"foreignObject"===s?"http://www.w3.org/1999/xhtml":e,f,g,f?f[0]:c.__k&&cJ(c,0),h,i),null!=f)for(j=f.length;j--;)cF(f[j]);h||(j="value","progress"===s&&null==o?a.removeAttribute("value"):void 0===o||o===a[j]&&("progress"!==s||o)&&("option"!==s||o===q[j])||cO(a,j,o,q[j],e),j="checked",void 0!==p&&p!==a[j]&&cO(a,j,p,q[j],e))}return a}(c.__e,b,c,d,e,f,g,i,j);(k=cr.diffed)&&k(b)}function cR(a,b,c){try{if("function"==typeof a){var d="function"==typeof a.__u;d&&a.__u(),d&&null==b||(a.__u=a(b))}else a.current=b}catch(a){cr.__e(a,c)}}function cS(a,b,c){return this.constructor(a,c)}cq=cB.slice,cr={__e:function(a,b,c,d){for(var e,f,g;b=b.__;)if((e=b.__c)&&!e.__)try{if((f=e.constructor)&&null!=f.getDerivedStateFromError&&(e.setState(f.getDerivedStateFromError(a)),g=e.__d),null!=e.componentDidCatch&&(e.componentDidCatch(a,d||{}),g=e.__d),g)return e.__E=e}catch(b){a=b}throw a}},cs=0,cI.prototype.setState=function(a,b){var c;c=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=cE({},this.state),"function"==typeof a&&(a=a(cE({},c),this.props)),a&&cE(c,a),null!=a&&this.__v&&(b&&this._sb.push(b),cK(this))},cI.prototype.forceUpdate=function(a){this.__v&&(this.__e=!0,a&&this.__h.push(a),cK(this))},cI.prototype.render=cH,ct=[],cv="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,cw=function(a,b){return a.__v.__b-b.__v.__b},cL.__r=0,cx=0,cy=cP(!1),cz=cP(!0);var cT=/[\s\n\\/='"\0<>]/,cU=/^(xlink|xmlns|xml)([A-Z])/,cV=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,cW=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,cX=new Set(["draggable","spellcheck"]),cY=/["&<]/;function cZ(a){if(0===a.length||!1===cY.test(a))return a;for(var b=0,c=0,d="",e="";c<a.length;c++){switch(a.charCodeAt(c)){case 34:e="&quot;";break;case 38:e="&amp;";break;case 60:e="&lt;";break;default:continue}c!==b&&(d+=a.slice(b,c)),d+=e,b=c+1}return c!==b&&(d+=a.slice(b,c)),d}var c$={},c_=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),c0=/[A-Z]/g;function c1(){this.__d=!0}var c2,c3,c4,c5,c6={},c7=[],c8=Array.isArray,c9=Object.assign;function da(a,b){var c,d=a.type,e=!0;return a.__c?(e=!1,(c=a.__c).state=c.__s):c=new d(a.props,b),a.__c=c,c.__v=a,c.props=a.props,c.context=b,c.__d=!0,null==c.state&&(c.state=c6),null==c.__s&&(c.__s=c.state),d.getDerivedStateFromProps?c.state=c9({},c.state,d.getDerivedStateFromProps(c.props,c.state)):e&&c.componentWillMount?(c.componentWillMount(),c.state=c.__s!==c.state?c.__s:c.state):!e&&c.componentWillUpdate&&c.componentWillUpdate(),c4&&c4(a),c.render(c.props,c.state,b)}var db=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),dc=0;function dd(a,b,c,d,e,f){b||(b={});var g,h,i=b;"ref"in b&&(g=b.ref,delete b.ref);var j={type:a,props:i,key:c,ref:g,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--dc,__i:-1,__u:0,__source:e,__self:f};if("function"==typeof a&&(g=a.defaultProps))for(h in g)void 0===i[h]&&(i[h]=g[h]);return cr.vnode&&cr.vnode(j),j}async function de(a,b){let c=window.SimpleWebAuthnBrowser;async function d(c){let d=new URL(`${a}/webauthn-options/${b}`);c&&d.searchParams.append("action",c),f().forEach(a=>{d.searchParams.append(a.name,a.value)});let e=await fetch(d);return e.ok?e.json():void console.error("Failed to fetch options",e)}function e(){let a=`#${b}-form`,c=document.querySelector(a);if(!c)throw Error(`Form '${a}' not found`);return c}function f(){return Array.from(e().querySelectorAll("input[data-form-field]"))}async function g(a,b){let c=e();if(a){let b=document.createElement("input");b.type="hidden",b.name="action",b.value=a,c.appendChild(b)}if(b){let a=document.createElement("input");a.type="hidden",a.name="data",a.value=JSON.stringify(b),c.appendChild(a)}return c.submit()}async function h(a,b){let d=await c.startAuthentication(a,b);return await g("authenticate",d)}async function i(a){f().forEach(a=>{if(a.required&&!a.value)throw Error(`Missing required field: ${a.name}`)});let b=await c.startRegistration(a);return await g("register",b)}async function j(){if(!c.browserSupportsWebAuthnAutofill())return;let a=await d("authenticate");if(!a)return void console.error("Failed to fetch option for autofill authentication");try{await h(a.options,!0)}catch(a){console.error(a)}}(async function(){let a=e();if(!c.browserSupportsWebAuthn()){a.style.display="none";return}a&&a.addEventListener("submit",async a=>{a.preventDefault();let b=await d(void 0);if(!b)return void console.error("Failed to fetch options for form submission");if("authenticate"===b.action)try{await h(b.options,!1)}catch(a){console.error(a)}else if("register"===b.action)try{await i(b.options)}catch(a){console.error(a)}})})(),j()}let df={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},dg=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function dh({html:a,title:b,status:c,cookies:d,theme:e,headTags:f}){return{cookies:d,status:c,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${dg}</style><title>${b}</title>${f??""}</head><body class="__next-auth-theme-${e?.colorScheme??"auto"}"><div class="page">${function(a,b,c){var d=cr.__s;cr.__s=!0,c2=cr.__b,c3=cr.diffed,c4=cr.__r,c5=cr.unmount;var e=function(a,b,c){var d,e,f,g={};for(f in b)"key"==f?d=b[f]:"ref"==f?e=b[f]:g[f]=b[f];if(arguments.length>2&&(g.children=arguments.length>3?cq.call(arguments,2):c),"function"==typeof a&&null!=a.defaultProps)for(f in a.defaultProps)void 0===g[f]&&(g[f]=a.defaultProps[f]);return cG(a,g,d,e,null)}(cH,null);e.__k=[a];try{var f=function a(b,c,d,e,f,g,h){if(null==b||!0===b||!1===b||""===b)return"";var i=typeof b;if("object"!=i)return"function"==i?"":"string"==i?cZ(b):b+"";if(c8(b)){var j,k="";f.__k=b;for(var l=0;l<b.length;l++){var m=b[l];if(null!=m&&"boolean"!=typeof m){var n,o=a(m,c,d,e,f,g,h);"string"==typeof o?k+=o:(j||(j=[]),k&&j.push(k),k="",c8(o)?(n=j).push.apply(n,o):j.push(o))}}return j?(k&&j.push(k),j):k}if(void 0!==b.constructor)return"";b.__=f,c2&&c2(b);var p=b.type,q=b.props;if("function"==typeof p){var r,s,t,u=c;if(p===cH){if("tpl"in q){for(var v="",w=0;w<q.tpl.length;w++)if(v+=q.tpl[w],q.exprs&&w<q.exprs.length){var x=q.exprs[w];if(null==x)continue;"object"==typeof x&&(void 0===x.constructor||c8(x))?v+=a(x,c,d,e,b,g,h):v+=x}return v}if("UNSTABLE_comment"in q)return"\x3c!--"+cZ(q.UNSTABLE_comment)+"--\x3e";s=q.children}else{if(null!=(r=p.contextType)){var y=c[r.__c];u=y?y.props.value:r.__}var z=p.prototype&&"function"==typeof p.prototype.render;if(z)s=da(b,u),t=b.__c;else{b.__c=t={__v:b,context:u,props:b.props,setState:c1,forceUpdate:c1,__d:!0,__h:[]};for(var A=0;t.__d&&A++<25;)t.__d=!1,c4&&c4(b),s=p.call(t,q,u);t.__d=!0}if(null!=t.getChildContext&&(c=c9({},c,t.getChildContext())),z&&cr.errorBoundaries&&(p.getDerivedStateFromError||t.componentDidCatch)){s=null!=s&&s.type===cH&&null==s.key&&null==s.props.tpl?s.props.children:s;try{return a(s,c,d,e,b,g,h)}catch(f){return p.getDerivedStateFromError&&(t.__s=p.getDerivedStateFromError(f)),t.componentDidCatch&&t.componentDidCatch(f,c6),t.__d?(s=da(b,c),null!=(t=b.__c).getChildContext&&(c=c9({},c,t.getChildContext())),a(s=null!=s&&s.type===cH&&null==s.key&&null==s.props.tpl?s.props.children:s,c,d,e,b,g,h)):""}finally{c3&&c3(b),b.__=null,c5&&c5(b)}}}s=null!=s&&s.type===cH&&null==s.key&&null==s.props.tpl?s.props.children:s;try{var B=a(s,c,d,e,b,g,h);return c3&&c3(b),b.__=null,cr.unmount&&cr.unmount(b),B}catch(f){if(!g&&h&&h.onError){var C=h.onError(f,b,function(f){return a(f,c,d,e,b,g,h)});if(void 0!==C)return C;var D=cr.__e;return D&&D(f,b),""}if(!g||!f||"function"!=typeof f.then)throw f;return f.then(function f(){try{return a(s,c,d,e,b,g,h)}catch(i){if(!i||"function"!=typeof i.then)throw i;return i.then(function(){return a(s,c,d,e,b,g,h)},f)}})}}var E,F="<"+p,G="";for(var H in q){var I=q[H];if("function"!=typeof I||"class"===H||"className"===H){switch(H){case"children":E=I;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in q)continue;H="for";break;case"className":if("class"in q)continue;H="class";break;case"defaultChecked":H="checked";break;case"defaultSelected":H="selected";break;case"defaultValue":case"value":switch(H="value",p){case"textarea":E=I;continue;case"select":e=I;continue;case"option":e!=I||"selected"in q||(F+=" selected")}break;case"dangerouslySetInnerHTML":G=I&&I.__html;continue;case"style":"object"==typeof I&&(I=function(a){var b="";for(var c in a){var d=a[c];if(null!=d&&""!==d){var e="-"==c[0]?c:c$[c]||(c$[c]=c.replace(c0,"-$&").toLowerCase()),f=";";"number"!=typeof d||e.startsWith("--")||c_.has(e)||(f="px;"),b=b+e+":"+d+f}}return b||void 0}(I));break;case"acceptCharset":H="accept-charset";break;case"httpEquiv":H="http-equiv";break;default:if(cU.test(H))H=H.replace(cU,"$1:$2").toLowerCase();else{if(cT.test(H))continue;("-"===H[4]||cX.has(H))&&null!=I?I+="":d?cW.test(H)&&(H="panose1"===H?"panose-1":H.replace(/([A-Z])/g,"-$1").toLowerCase()):cV.test(H)&&(H=H.toLowerCase())}}null!=I&&!1!==I&&(F=!0===I||""===I?F+" "+H:F+" "+H+'="'+("string"==typeof I?cZ(I):I+"")+'"')}}if(cT.test(p))throw Error(p+" is not a valid HTML tag name in "+F+">");if(G||("string"==typeof E?G=cZ(E):null!=E&&!1!==E&&!0!==E&&(G=a(E,c,"svg"===p||"foreignObject"!==p&&d,e,b,g,h))),c3&&c3(b),b.__=null,c5&&c5(b),!G&&db.has(p))return F+"/>";var J="</"+p+">",K=F+">";return c8(G)?[K].concat(G,[J]):"string"!=typeof G?[K,G,J]:K+G+J}(a,c6,!1,void 0,e,!1,void 0);return c8(f)?f.join(""):f}catch(a){if(a.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw a}finally{cr.__c&&cr.__c(a,c7),cr.__s=d,c7.length=0}}(a)}</div></body></html>`}}function di(a){let{url:b,theme:c,query:d,cookies:e,pages:f,providers:g}=a;return{csrf:(a,b,c)=>a?(b.logger.warn("csrf-disabled"),c.push({name:b.cookies.csrfToken.name,value:"",options:{...b.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:c}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:b.csrfToken},cookies:c},providers:a=>({headers:{"Content-Type":"application/json"},body:a.reduce((a,{id:b,name:c,type:d,signinUrl:e,callbackUrl:f})=>(a[b]={id:b,name:c,type:d,signinUrl:e,callbackUrl:f},a),{})}),signin(b,h){if(b)throw new K("Unsupported action");if(f?.signIn){let b=`${f.signIn}${f.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:a.callbackUrl??"/"})}`;return h&&(b=`${b}&${new URLSearchParams({error:h})}`),{redirect:b,cookies:e}}let i=g?.find(a=>"webauthn"===a.type&&a.enableConditionalUI&&!!a.simpleWebAuthnBrowserVersion),j="";if(i){let{simpleWebAuthnBrowserVersion:a}=i;j=`<script src="https://unpkg.com/@simplewebauthn/browser@${a}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return dh({cookies:e,theme:c,html:function(a){let{csrfToken:b,providers:c=[],callbackUrl:d,theme:e,email:f,error:g}=a;"undefined"!=typeof document&&e?.brandColor&&document.documentElement.style.setProperty("--brand-color",e.brandColor),"undefined"!=typeof document&&e?.buttonText&&document.documentElement.style.setProperty("--button-text-color",e.buttonText);let h=g&&(df[g]??df.default),i=c.find(a=>"webauthn"===a.type&&a.enableConditionalUI)?.id;return dd("div",{className:"signin",children:[e?.brandColor&&dd("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${e.brandColor}}`}}),e?.buttonText&&dd("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${e.buttonText}
        }
      `}}),dd("div",{className:"card",children:[h&&dd("div",{className:"error",children:dd("p",{children:h})}),e?.logo&&dd("img",{src:e.logo,alt:"Logo",className:"logo"}),c.map((a,e)=>{let g,h,i;("oauth"===a.type||"oidc"===a.type)&&({bg:g="#fff",brandColor:h,logo:i=`https://authjs.dev/img/providers/${a.id}.svg`}=a.style??{});let j=h??g??"#fff";return dd("div",{className:"provider",children:["oauth"===a.type||"oidc"===a.type?dd("form",{action:a.signinUrl,method:"POST",children:[dd("input",{type:"hidden",name:"csrfToken",value:b}),d&&dd("input",{type:"hidden",name:"callbackUrl",value:d}),dd("button",{type:"submit",className:"button",style:{"--provider-brand-color":j},tabIndex:0,children:[dd("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",a.name]}),i&&dd("img",{loading:"lazy",height:24,src:i})]})]}):null,("email"===a.type||"credentials"===a.type||"webauthn"===a.type)&&e>0&&"email"!==c[e-1].type&&"credentials"!==c[e-1].type&&"webauthn"!==c[e-1].type&&dd("hr",{}),"email"===a.type&&dd("form",{action:a.signinUrl,method:"POST",children:[dd("input",{type:"hidden",name:"csrfToken",value:b}),dd("label",{className:"section-header",htmlFor:`input-email-for-${a.id}-provider`,children:"Email"}),dd("input",{id:`input-email-for-${a.id}-provider`,autoFocus:!0,type:"email",name:"email",value:f,placeholder:"<EMAIL>",required:!0}),dd("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),"credentials"===a.type&&dd("form",{action:a.callbackUrl,method:"POST",children:[dd("input",{type:"hidden",name:"csrfToken",value:b}),Object.keys(a.credentials).map(b=>dd("div",{children:[dd("label",{className:"section-header",htmlFor:`input-${b}-for-${a.id}-provider`,children:a.credentials[b].label??b}),dd("input",{name:b,id:`input-${b}-for-${a.id}-provider`,type:a.credentials[b].type??"text",placeholder:a.credentials[b].placeholder??"",...a.credentials[b]})]},`input-group-${a.id}`)),dd("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),"webauthn"===a.type&&dd("form",{action:a.callbackUrl,method:"POST",id:`${a.id}-form`,children:[dd("input",{type:"hidden",name:"csrfToken",value:b}),Object.keys(a.formFields).map(b=>dd("div",{children:[dd("label",{className:"section-header",htmlFor:`input-${b}-for-${a.id}-provider`,children:a.formFields[b].label??b}),dd("input",{name:b,"data-form-field":!0,id:`input-${b}-for-${a.id}-provider`,type:a.formFields[b].type??"text",placeholder:a.formFields[b].placeholder??"",...a.formFields[b]})]},`input-group-${a.id}`)),dd("button",{id:`submitButton-${a.id}`,type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),("email"===a.type||"credentials"===a.type||"webauthn"===a.type)&&e+1<c.length&&dd("hr",{})]},a.id)})]}),i&&dd(cH,{children:dd("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${de})(authURL, "${i}");
`}})})]})}({csrfToken:a.csrfToken,providers:a.providers?.filter(a=>["email","oauth","oidc"].includes(a.type)||"credentials"===a.type&&a.credentials||"webauthn"===a.type&&a.formFields||!1),callbackUrl:a.callbackUrl,theme:a.theme,error:h,...d}),title:"Sign In",headTags:j})},signout:()=>f?.signOut?{redirect:f.signOut,cookies:e}:dh({cookies:e,theme:c,html:function(a){let{url:b,csrfToken:c,theme:d}=a;return dd("div",{className:"signout",children:[d?.brandColor&&dd("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${d.brandColor}
        }
      `}}),d?.buttonText&&dd("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${d.buttonText}
        }
      `}}),dd("div",{className:"card",children:[d?.logo&&dd("img",{src:d.logo,alt:"Logo",className:"logo"}),dd("h1",{children:"Signout"}),dd("p",{children:"Are you sure you want to sign out?"}),dd("form",{action:b?.toString(),method:"POST",children:[dd("input",{type:"hidden",name:"csrfToken",value:c}),dd("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:a.csrfToken,url:b,theme:c}),title:"Sign Out"}),verifyRequest:a=>f?.verifyRequest?{redirect:`${f.verifyRequest}${b?.search??""}`,cookies:e}:dh({cookies:e,theme:c,html:function(a){let{url:b,theme:c}=a;return dd("div",{className:"verify-request",children:[c.brandColor&&dd("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${c.brandColor}
        }
      `}}),dd("div",{className:"card",children:[c.logo&&dd("img",{src:c.logo,alt:"Logo",className:"logo"}),dd("h1",{children:"Check your email"}),dd("p",{children:"A sign in link has been sent to your email address."}),dd("p",{children:dd("a",{className:"site",href:b.origin,children:b.host})})]})]})}({url:b,theme:c,...a}),title:"Verify Request"}),error:a=>f?.error?{redirect:`${f.error}${f.error.includes("?")?"&":"?"}error=${a}`,cookies:e}:dh({cookies:e,theme:c,...function(a){let{url:b,error:c="default",theme:d}=a,e=`${b}/signin`,f={default:{status:200,heading:"Error",message:dd("p",{children:dd("a",{className:"site",href:b?.origin,children:b?.host})})},Configuration:{status:500,heading:"Server error",message:dd("div",{children:[dd("p",{children:"There is a problem with the server configuration."}),dd("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:dd("div",{children:[dd("p",{children:"You do not have permission to sign in."}),dd("p",{children:dd("a",{className:"button",href:e,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:dd("div",{children:[dd("p",{children:"The sign in link is no longer valid."}),dd("p",{children:"It may have been used already or it may have expired."})]}),signin:dd("a",{className:"button",href:e,children:"Sign in"})}},{status:g,heading:h,message:i,signin:j}=f[c]??f.default;return{status:g,html:dd("div",{className:"error",children:[d?.brandColor&&dd("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${d?.brandColor}
        }
      `}}),dd("div",{className:"card",children:[d?.logo&&dd("img",{src:d?.logo,alt:"Logo",className:"logo"}),dd("h1",{children:h}),dd("div",{className:"message",children:i}),j]})]})}}({url:b,theme:c,error:a}),title:"Error"})}}function dj(a,b=Date.now()){return new Date(b+1e3*a)}async function dk(a,b,c,d){if(!c?.providerAccountId||!c.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(c.type))throw Error("Provider not supported");let{adapter:e,jwt:f,events:g,session:{strategy:h,generateSessionToken:i}}=d;if(!e)return{user:b,account:c};let j=c,{createUser:k,updateUser:l,getUser:m,getUserByAccount:n,getUserByEmail:o,linkAccount:p,createSession:q,getSessionAndUser:r,deleteSession:s}=e,t=null,u=null,v=!1,w="jwt"===h;if(a)if(w)try{let b=d.cookies.sessionToken.name;(t=await f.decode({...f,token:a,salt:b}))&&"sub"in t&&t.sub&&(u=await m(t.sub))}catch{}else{let b=await r(a);b&&(t=b.session,u=b.user)}if("email"===j.type){let c=await o(b.email);return c?(u?.id!==c.id&&!w&&a&&await s(a),u=await l({id:c.id,emailVerified:new Date}),await g.updateUser?.({user:u})):(u=await k({...b,emailVerified:new Date}),await g.createUser?.({user:u}),v=!0),{session:t=w?{}:await q({sessionToken:i(),userId:u.id,expires:dj(d.session.maxAge)}),user:u,isNewUser:v}}if("webauthn"===j.type){let a=await n({providerAccountId:j.providerAccountId,provider:j.provider});if(a){if(u){if(a.id===u.id){let a={...j,userId:u.id};return{session:t,user:u,isNewUser:v,account:a}}throw new U("The account is already associated with another user",{provider:j.provider})}t=w?{}:await q({sessionToken:i(),userId:a.id,expires:dj(d.session.maxAge)});let b={...j,userId:a.id};return{session:t,user:a,isNewUser:v,account:b}}{if(u){await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b});let a={...j,userId:u.id};return{session:t,user:u,isNewUser:v,account:a}}if(b.email?await o(b.email):null)throw new U("Another account already exists with the same e-mail address",{provider:j.provider});u=await k({...b}),await g.createUser?.({user:u}),await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b}),t=w?{}:await q({sessionToken:i(),userId:u.id,expires:dj(d.session.maxAge)});let a={...j,userId:u.id};return{session:t,user:u,isNewUser:!0,account:a}}}let x=await n({providerAccountId:j.providerAccountId,provider:j.provider});if(x){if(u){if(x.id===u.id)return{session:t,user:u,isNewUser:v};throw new D("The account is already associated with another user",{provider:j.provider})}return{session:t=w?{}:await q({sessionToken:i(),userId:x.id,expires:dj(d.session.maxAge)}),user:x,isNewUser:v}}{let{provider:a}=d,{type:c,provider:e,providerAccountId:f,userId:h,...l}=j;if(j=Object.assign(a.account(l)??{},{providerAccountId:f,provider:e,type:c,userId:h}),u)return await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b}),{session:t,user:u,isNewUser:v};let m=b.email?await o(b.email):null;if(m){let a=d.provider;if(a?.allowDangerousEmailAccountLinking)u=m,v=!1;else throw new D("Another account already exists with the same e-mail address",{provider:j.provider})}else u=await k({...b,emailVerified:null}),v=!0;return await g.createUser?.({user:u}),await p({...j,userId:u.id}),await g.linkAccount?.({user:u,account:j,profile:b}),{session:t=w?{}:await q({sessionToken:i(),userId:u.id,expires:dj(d.session.maxAge)}),user:u,isNewUser:v}}}function dl(a,b){if(null==a)return!1;try{return a instanceof b||Object.getPrototypeOf(a)[Symbol.toStringTag]===b.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(f="oauth4webapi/v3.6.0");let dm="ERR_INVALID_ARG_VALUE",dn="ERR_INVALID_ARG_TYPE";function dp(a,b,c){let d=TypeError(a,{cause:c});return Object.assign(d,{code:b}),d}let dq=Symbol(),dr=Symbol(),ds=Symbol(),dt=Symbol(),du=Symbol(),dv=Symbol();Symbol();let dw=new TextEncoder,dx=new TextDecoder;function dy(a){return"string"==typeof a?dw.encode(a):dx.decode(a)}function dz(a){return"string"==typeof a?h(a):g(a)}g=Uint8Array.prototype.toBase64?a=>(a instanceof ArrayBuffer&&(a=new Uint8Array(a)),a.toBase64({alphabet:"base64url",omitPadding:!0})):a=>{a instanceof ArrayBuffer&&(a=new Uint8Array(a));let b=[];for(let c=0;c<a.byteLength;c+=32768)b.push(String.fromCharCode.apply(null,a.subarray(c,c+32768)));return btoa(b.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},h=Uint8Array.fromBase64?a=>{try{return Uint8Array.fromBase64(a,{alphabet:"base64url"})}catch(a){throw dp("The input to be decoded is not correctly encoded.",dm,a)}}:a=>{try{let b=atob(a.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),c=new Uint8Array(b.length);for(let a=0;a<b.length;a++)c[a]=b.charCodeAt(a);return c}catch(a){throw dp("The input to be decoded is not correctly encoded.",dm,a)}};class dA extends Error{code;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=eB,Error.captureStackTrace?.(this,this.constructor)}}class dB extends Error{code;constructor(a,b){super(a,b),this.name=this.constructor.name,b?.code&&(this.code=b?.code),Error.captureStackTrace?.(this,this.constructor)}}function dC(a,b,c){return new dB(a,{code:b,cause:c})}function dD(a){return!(null===a||"object"!=typeof a||Array.isArray(a))}function dE(a){dl(a,Headers)&&(a=Object.fromEntries(a.entries()));let b=new Headers(a??{});if(f&&!b.has("user-agent")&&b.set("user-agent",f),b.has("authorization"))throw dp('"options.headers" must not include the "authorization" header name',dm);return b}function dF(a,b){if(void 0!==b){if("function"==typeof b&&(b=b(a.href)),!(b instanceof AbortSignal))throw dp('"options.signal" must return or be an instance of AbortSignal',dn);return b}}function dG(a){return a.includes("//")?a.replace("//","/"):a}async function dH(a,b,c,d){if(!(a instanceof URL))throw dp(`"${b}" must be an instance of URL`,dn);dX(a,d?.[dq]!==!0);let e=c(new URL(a.href)),f=dE(d?.headers);return f.set("accept","application/json"),(d?.[dt]||fetch)(e.href,{body:void 0,headers:Object.fromEntries(f.entries()),method:"GET",redirect:"manual",signal:dF(e,d?.signal)})}async function dI(a,b){return dH(a,"issuerIdentifier",a=>{switch(b?.algorithm){case void 0:case"oidc":a.pathname=dG(`${a.pathname}/.well-known/openid-configuration`);break;case"oauth2":!function(a,b,c=!1){"/"===a.pathname?a.pathname=b:a.pathname=dG(`${b}/${c?a.pathname:a.pathname.replace(/(\/)$/,"")}`)}(a,".well-known/oauth-authorization-server");break;default:throw dp('"options.algorithm" must be "oidc" (default), or "oauth2"',dm)}return a},b)}function dJ(a,b,c,d,e){try{if("number"!=typeof a||!Number.isFinite(a))throw dp(`${c} must be a number`,dn,e);if(a>0)return;if(b){if(0!==a)throw dp(`${c} must be a non-negative number`,dm,e);return}throw dp(`${c} must be a positive number`,dm,e)}catch(a){if(d)throw dC(a.message,d,e);throw a}}function dK(a,b,c,d){try{if("string"!=typeof a)throw dp(`${b} must be a string`,dn,d);if(0===a.length)throw dp(`${b} must not be empty`,dm,d)}catch(a){if(c)throw dC(a.message,c,d);throw a}}async function dL(a,b){if(!(a instanceof URL)&&a!==eX)throw dp('"expectedIssuerIdentifier" must be an instance of URL',dn);if(!dl(b,Response))throw dp('"response" must be an instance of Response',dn);if(200!==b.status)throw dC('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',eH,b);eP(b);let c=await eW(b);if(dK(c.issuer,'"response" body "issuer" property',eF,{body:c}),a!==eX&&new URL(c.issuer).href!==a.href)throw dC('"response" body "issuer" property does not match the expected value',eM,{expected:a.href,body:c,attribute:"issuer"});return c}function dM(a){var b=a,c="application/json";if(ec(b)!==c)throw function(a,...b){let c='"response" content-type must be ';if(b.length>2){let a=b.pop();c+=`${b.join(", ")}, or ${a}`}else 2===b.length?c+=`${b[0]} or ${b[1]}`:c+=b[0];return dC(c,eG,a)}(b,c)}function dN(){return dz(crypto.getRandomValues(new Uint8Array(32)))}async function dO(a){return dK(a,"codeVerifier"),dz(await crypto.subtle.digest("SHA-256",dy(a)))}function dP(a){let b=a?.[dr];return"number"==typeof b&&Number.isFinite(b)?b:0}function dQ(a){let b=a?.[ds];return"number"==typeof b&&Number.isFinite(b)&&-1!==Math.sign(b)?b:30}function dR(){return Math.floor(Date.now()/1e3)}function dS(a){if("object"!=typeof a||null===a)throw dp('"as" must be an object',dn);dK(a.issuer,'"as.issuer"')}function dT(a){if("object"!=typeof a||null===a)throw dp('"client" must be an object',dn);dK(a.client_id,'"client.client_id"')}function dU(a,b){let c=dR()+dP(b);return{jti:dN(),aud:a.issuer,exp:c+60,iat:c,nbf:c,iss:b.client_id,sub:b.client_id}}async function dV(a,b,c){if(!c.usages.includes("sign"))throw dp('CryptoKey instances used for signing assertions must include "sign" in their "usages"',dm);let d=`${dz(dy(JSON.stringify(a)))}.${dz(dy(JSON.stringify(b)))}`,e=dz(await crypto.subtle.sign(function(a){switch(a.algorithm.name){case"ECDSA":return{name:a.algorithm.name,hash:function(a){let{algorithm:b}=a;switch(b.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new dA("unsupported ECDSA namedCurve",{cause:a})}}(a)};case"RSA-PSS":switch(eQ(a),a.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:a.algorithm.name,saltLength:parseInt(a.algorithm.hash.name.slice(-3),10)>>3};default:throw new dA("unsupported RSA-PSS hash name",{cause:a})}case"RSASSA-PKCS1-v1_5":return eQ(a),a.algorithm.name;case"Ed25519":return a.algorithm.name}throw new dA("unsupported CryptoKey algorithm name",{cause:a})}(c),c,dy(d)));return`${d}.${e}`}let dW=URL.parse?(a,b)=>URL.parse(a,b):(a,b)=>{try{return new URL(a,b)}catch{return null}};function dX(a,b){if(b&&"https:"!==a.protocol)throw dC("only requests to HTTPS are allowed",eI,a);if("https:"!==a.protocol&&"http:"!==a.protocol)throw dC("only HTTP and HTTPS requests are allowed",eJ,a)}function dY(a,b,c,d){let e;if("string"!=typeof a||!(e=dW(a)))throw dC(`authorization server metadata does not contain a valid ${c?`"as.mtls_endpoint_aliases.${b}"`:`"as.${b}"`}`,void 0===a?eN:eO,{attribute:c?`mtls_endpoint_aliases.${b}`:b});return dX(e,d),e}function dZ(a,b,c,d){return c&&a.mtls_endpoint_aliases&&b in a.mtls_endpoint_aliases?dY(a.mtls_endpoint_aliases[b],b,c,d):dY(a[b],b,c,d)}class d$ extends Error{cause;code;error;status;error_description;response;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=eA,this.cause=b.cause,this.error=b.cause.error,this.status=b.response.status,this.error_description=b.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:b.response}),Error.captureStackTrace?.(this,this.constructor)}}class d_ extends Error{cause;code;error;error_description;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=eC,this.cause=b.cause,this.error=b.cause.get("error"),this.error_description=b.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class d0 extends Error{cause;code;response;status;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=ez,this.cause=b.cause,this.status=b.response.status,this.response=b.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let d1="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",d2=RegExp("^[,\\s]*("+d1+")\\s(.*)"),d3=RegExp("^[,\\s]*("+d1+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),d4=RegExp("^[,\\s]*"+("("+d1+")\\s*=\\s*(")+d1+")[,\\s]*(.*)"),d5=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function d6(a){if(a.status>399&&a.status<500){eP(a),dM(a);try{let b=await a.clone().json();if(dD(b)&&"string"==typeof b.error&&b.error.length)return b}catch{}}}async function d7(a,b,c){if(a.status!==b){let b;if(b=await d6(a))throw await a.body?.cancel(),new d$("server responded with an error in the response body",{cause:b,response:a});throw dC(`"response" is not a conform ${c} response (unexpected HTTP status code)`,eH,a)}}function d8(a){if(!ep.has(a))throw dp('"options.DPoP" is not a valid DPoPHandle',dm)}async function d9(a,b,c,d,e,f){if(dK(a,'"accessToken"'),!(c instanceof URL))throw dp('"url" must be an instance of URL',dn);dX(c,f?.[dq]!==!0),d=dE(d),f?.DPoP&&(d8(f.DPoP),await f.DPoP.addProof(c,d,b.toUpperCase(),a)),d.set("authorization",`${d.has("dpop")?"DPoP":"Bearer"} ${a}`);let g=await (f?.[dt]||fetch)(c.href,{body:e,headers:Object.fromEntries(d.entries()),method:b,redirect:"manual",signal:dF(c,f?.signal)});return f?.DPoP?.cacheNonce(g),g}async function ea(a,b,c,d){dS(a),dT(b);let e=dZ(a,"userinfo_endpoint",b.use_mtls_endpoint_aliases,d?.[dq]!==!0),f=dE(d?.headers);return b.userinfo_signed_response_alg?f.set("accept","application/jwt"):(f.set("accept","application/json"),f.append("accept","application/jwt")),d9(c,"GET",e,f,null,{...d,[dr]:dP(b)})}let eb=Symbol();function ec(a){return a.headers.get("content-type")?.split(";")[0]}async function ed(a,b,c,d,e){let f;if(dS(a),dT(b),!dl(d,Response))throw dp('"response" must be an instance of Response',dn);if(ek(d),200!==d.status)throw dC('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',eH,d);if(eP(d),"application/jwt"===ec(d)){let{claims:c,jwt:g}=await eR(await d.text(),eS.bind(void 0,b.userinfo_signed_response_alg,a.userinfo_signing_alg_values_supported,void 0),dP(b),dQ(b),e?.[dv]).then(el.bind(void 0,b.client_id)).then(en.bind(void 0,a));eh.set(d,g),f=c}else{if(b.userinfo_signed_response_alg)throw dC("JWT UserInfo Response expected",eD,d);f=await eW(d)}if(dK(f.sub,'"response" body "sub" property',eF,{body:f}),c===eb);else if(dK(c,'"expectedSubject"'),f.sub!==c)throw dC('unexpected "response" body "sub" property value',eM,{expected:c,body:f,attribute:"sub"});return f}async function ee(a,b,c,d,e,f,g){return await c(a,b,e,f),f.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(g?.[dt]||fetch)(d.href,{body:e,headers:Object.fromEntries(f.entries()),method:"POST",redirect:"manual",signal:dF(d,g?.signal)})}async function ef(a,b,c,d,e,f){let g=dZ(a,"token_endpoint",b.use_mtls_endpoint_aliases,f?.[dq]!==!0);e.set("grant_type",d);let h=dE(f?.headers);h.set("accept","application/json"),f?.DPoP!==void 0&&(d8(f.DPoP),await f.DPoP.addProof(g,h,"POST"));let i=await ee(a,b,c,g,e,h,f);return f?.DPoP?.cacheNonce(i),i}let eg=new WeakMap,eh=new WeakMap;function ei(a){if(!a.id_token)return;let b=eg.get(a);if(!b)throw dp('"ref" was already garbage collected or did not resolve from the proper sources',dm);return b}async function ej(a,b,c,d,e){if(dS(a),dT(b),!dl(c,Response))throw dp('"response" must be an instance of Response',dn);ek(c),await d7(c,200,"Token Endpoint"),eP(c);let f=await eW(c);if(dK(f.access_token,'"response" body "access_token" property',eF,{body:f}),dK(f.token_type,'"response" body "token_type" property',eF,{body:f}),f.token_type=f.token_type.toLowerCase(),"dpop"!==f.token_type&&"bearer"!==f.token_type)throw new dA("unsupported `token_type` value",{cause:{body:f}});if(void 0!==f.expires_in){let a="number"!=typeof f.expires_in?parseFloat(f.expires_in):f.expires_in;dJ(a,!0,'"response" body "expires_in" property',eF,{body:f}),f.expires_in=a}if(void 0!==f.refresh_token&&dK(f.refresh_token,'"response" body "refresh_token" property',eF,{body:f}),void 0!==f.scope&&"string"!=typeof f.scope)throw dC('"response" body "scope" property must be a string',eF,{body:f});if(void 0!==f.id_token){dK(f.id_token,'"response" body "id_token" property',eF,{body:f});let g=["aud","exp","iat","iss","sub"];!0===b.require_auth_time&&g.push("auth_time"),void 0!==b.default_max_age&&(dJ(b.default_max_age,!0,'"client.default_max_age"'),g.push("auth_time")),d?.length&&g.push(...d);let{claims:h,jwt:i}=await eR(f.id_token,eS.bind(void 0,b.id_token_signed_response_alg,a.id_token_signing_alg_values_supported,"RS256"),dP(b),dQ(b),e?.[dv]).then(et.bind(void 0,g)).then(eo.bind(void 0,a)).then(em.bind(void 0,b.client_id));if(Array.isArray(h.aud)&&1!==h.aud.length){if(void 0===h.azp)throw dC('ID Token "aud" (audience) claim includes additional untrusted audiences',eL,{claims:h,claim:"aud"});if(h.azp!==b.client_id)throw dC('unexpected ID Token "azp" (authorized party) claim value',eL,{expected:b.client_id,claims:h,claim:"azp"})}void 0!==h.auth_time&&dJ(h.auth_time,!1,'ID Token "auth_time" (authentication time)',eF,{claims:h}),eh.set(c,i),eg.set(f,h)}return f}function ek(a){let b;if(b=function(a){if(!dl(a,Response))throw dp('"response" must be an instance of Response',dn);let b=a.headers.get("www-authenticate");if(null===b)return;let c=[],d=b;for(;d;){let a,b=d.match(d2),e=b?.["1"].toLowerCase();if(d=b?.["2"],!e)return;let f={};for(;d;){let c,e;if(b=d.match(d3)){if([,c,e,d]=b,e.includes("\\"))try{e=JSON.parse(`"${e}"`)}catch{}f[c.toLowerCase()]=e;continue}if(b=d.match(d4)){[,c,e,d]=b,f[c.toLowerCase()]=e;continue}if(b=d.match(d5)){if(Object.keys(f).length)break;[,a,d]=b;break}return}let g={scheme:e,parameters:f};a&&(g.token68=a),c.push(g)}if(c.length)return c}(a))throw new d0("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:b,response:a})}function el(a,b){return void 0!==b.claims.aud?em(a,b):b}function em(a,b){if(Array.isArray(b.claims.aud)){if(!b.claims.aud.includes(a))throw dC('unexpected JWT "aud" (audience) claim value',eL,{expected:a,claims:b.claims,claim:"aud"})}else if(b.claims.aud!==a)throw dC('unexpected JWT "aud" (audience) claim value',eL,{expected:a,claims:b.claims,claim:"aud"});return b}function en(a,b){return void 0!==b.claims.iss?eo(a,b):b}function eo(a,b){let c=a[eY]?.(b)??a.issuer;if(b.claims.iss!==c)throw dC('unexpected JWT "iss" (issuer) claim value',eL,{expected:c,claims:b.claims,claim:"iss"});return b}let ep=new WeakSet,eq=Symbol();async function er(a,b,c,d,e,f,g){if(dS(a),dT(b),!ep.has(d))throw dp('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',dm);dK(e,'"redirectUri"');let h=eT(d,"code");if(!h)throw dC('no authorization code in "callbackParameters"',eF);let i=new URLSearchParams(g?.additionalParameters);return i.set("redirect_uri",e),i.set("code",h),f!==eq&&(dK(f,'"codeVerifier"'),i.set("code_verifier",f)),ef(a,b,c,"authorization_code",i,g)}let es={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function et(a,b){for(let c of a)if(void 0===b.claims[c])throw dC(`JWT "${c}" (${es[c]}) claim missing`,eF,{claims:b.claims});return b}let eu=Symbol(),ev=Symbol();async function ew(a,b,c,d){return"string"==typeof d?.expectedNonce||"number"==typeof d?.maxAge||d?.requireIdToken?ex(a,b,c,d.expectedNonce,d.maxAge,{[dv]:d[dv]}):ey(a,b,c,d)}async function ex(a,b,c,d,e,f){let g=[];switch(d){case void 0:d=eu;break;case eu:break;default:dK(d,'"expectedNonce" argument'),g.push("nonce")}switch(e??=b.default_max_age){case void 0:e=ev;break;case ev:break;default:dJ(e,!0,'"maxAge" argument'),g.push("auth_time")}let h=await ej(a,b,c,g,f);dK(h.id_token,'"response" body "id_token" property',eF,{body:h});let i=ei(h);if(e!==ev){let a=dR()+dP(b),c=dQ(b);if(i.auth_time+e<a-c)throw dC("too much time has elapsed since the last End-User authentication",eK,{claims:i,now:a,tolerance:c,claim:"auth_time"})}if(d===eu){if(void 0!==i.nonce)throw dC('unexpected ID Token "nonce" claim value',eL,{expected:void 0,claims:i,claim:"nonce"})}else if(i.nonce!==d)throw dC('unexpected ID Token "nonce" claim value',eL,{expected:d,claims:i,claim:"nonce"});return h}async function ey(a,b,c,d){let e=await ej(a,b,c,void 0,d),f=ei(e);if(f){if(void 0!==b.default_max_age){dJ(b.default_max_age,!0,'"client.default_max_age"');let a=dR()+dP(b),c=dQ(b);if(f.auth_time+b.default_max_age<a-c)throw dC("too much time has elapsed since the last End-User authentication",eK,{claims:f,now:a,tolerance:c,claim:"auth_time"})}if(void 0!==f.nonce)throw dC('unexpected ID Token "nonce" claim value',eL,{expected:void 0,claims:f,claim:"nonce"})}return e}let ez="OAUTH_WWW_AUTHENTICATE_CHALLENGE",eA="OAUTH_RESPONSE_BODY_ERROR",eB="OAUTH_UNSUPPORTED_OPERATION",eC="OAUTH_AUTHORIZATION_RESPONSE_ERROR",eD="OAUTH_JWT_USERINFO_EXPECTED",eE="OAUTH_PARSE_ERROR",eF="OAUTH_INVALID_RESPONSE",eG="OAUTH_RESPONSE_IS_NOT_JSON",eH="OAUTH_RESPONSE_IS_NOT_CONFORM",eI="OAUTH_HTTP_REQUEST_FORBIDDEN",eJ="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",eK="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",eL="OAUTH_JWT_CLAIM_COMPARISON_FAILED",eM="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",eN="OAUTH_MISSING_SERVER_METADATA",eO="OAUTH_INVALID_SERVER_METADATA";function eP(a){if(a.bodyUsed)throw dp('"response" body has been used already',dm)}function eQ(a){let{algorithm:b}=a;if("number"!=typeof b.modulusLength||b.modulusLength<2048)throw new dA(`unsupported ${b.name} modulusLength`,{cause:a})}async function eR(a,b,c,d,e){let f,g,{0:h,1:i,length:j}=a.split(".");if(5===j)if(void 0!==e)a=await e(a),{0:h,1:i,length:j}=a.split(".");else throw new dA("JWE decryption is not configured",{cause:a});if(3!==j)throw dC("Invalid JWT",eF,a);try{f=JSON.parse(dy(dz(h)))}catch(a){throw dC("failed to parse JWT Header body as base64url encoded JSON",eE,a)}if(!dD(f))throw dC("JWT Header must be a top level object",eF,a);if(b(f),void 0!==f.crit)throw new dA('no JWT "crit" header parameter extensions are supported',{cause:{header:f}});try{g=JSON.parse(dy(dz(i)))}catch(a){throw dC("failed to parse JWT Payload body as base64url encoded JSON",eE,a)}if(!dD(g))throw dC("JWT Payload must be a top level object",eF,a);let k=dR()+c;if(void 0!==g.exp){if("number"!=typeof g.exp)throw dC('unexpected JWT "exp" (expiration time) claim type',eF,{claims:g});if(g.exp<=k-d)throw dC('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',eK,{claims:g,now:k,tolerance:d,claim:"exp"})}if(void 0!==g.iat&&"number"!=typeof g.iat)throw dC('unexpected JWT "iat" (issued at) claim type',eF,{claims:g});if(void 0!==g.iss&&"string"!=typeof g.iss)throw dC('unexpected JWT "iss" (issuer) claim type',eF,{claims:g});if(void 0!==g.nbf){if("number"!=typeof g.nbf)throw dC('unexpected JWT "nbf" (not before) claim type',eF,{claims:g});if(g.nbf>k+d)throw dC('unexpected JWT "nbf" (not before) claim value',eK,{claims:g,now:k,tolerance:d,claim:"nbf"})}if(void 0!==g.aud&&"string"!=typeof g.aud&&!Array.isArray(g.aud))throw dC('unexpected JWT "aud" (audience) claim type',eF,{claims:g});return{header:f,claims:g,jwt:a}}function eS(a,b,c,d){if(void 0!==a){if("string"==typeof a?d.alg!==a:!a.includes(d.alg))throw dC('unexpected JWT "alg" header parameter',eF,{header:d,expected:a,reason:"client configuration"});return}if(Array.isArray(b)){if(!b.includes(d.alg))throw dC('unexpected JWT "alg" header parameter',eF,{header:d,expected:b,reason:"authorization server metadata"});return}if(void 0!==c){if("string"==typeof c?d.alg!==c:"function"==typeof c?!c(d.alg):!c.includes(d.alg))throw dC('unexpected JWT "alg" header parameter',eF,{header:d,expected:c,reason:"default value"});return}throw dC('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:a,issuer:b,fallback:c})}function eT(a,b){let{0:c,length:d}=a.getAll(b);if(d>1)throw dC(`"${b}" parameter must be provided only once`,eF);return c}let eU=Symbol(),eV=Symbol();async function eW(a,b=dM){let c;try{c=await a.json()}catch(c){throw b(a),dC('failed to parse "response" body as JSON',eE,c)}if(!dD(c))throw dC('"response" body must be a top level object',eF,{body:c});return c}let eX=Symbol(),eY=Symbol();async function eZ(a,b,c){let{cookies:d,logger:e}=c,f=d[a],g=new Date;g.setTime(g.getTime()+9e5),e.debug(`CREATE_${a.toUpperCase()}`,{name:f.name,payload:b,COOKIE_TTL:900,expires:g});let h=await bS({...c.jwt,maxAge:900,token:{value:b},salt:f.name}),i={...f.options,expires:g};return{name:f.name,value:h,options:i}}async function e$(a,b,c){try{let{logger:d,cookies:e,jwt:f}=c;if(d.debug(`PARSE_${a.toUpperCase()}`,{cookie:b}),!b)throw new x(`${a} cookie was missing`);let g=await bT({...f,token:b,salt:e[a].name});if(g?.value)return g.value;throw Error("Invalid cookie")}catch(b){throw new x(`${a} value could not be parsed`,{cause:b})}}function e_(a,b,c){let{logger:d,cookies:e}=b,f=e[a];d.debug(`CLEAR_${a.toUpperCase()}`,{cookie:f}),c.push({name:f.name,value:"",options:{...e[a].options,maxAge:0}})}function e0(a,b){return async function(c,d,e){let{provider:f,logger:g}=e;if(!f?.checks?.includes(a))return;let h=c?.[e.cookies[b].name];g.debug(`USE_${b.toUpperCase()}`,{value:h});let i=await e$(b,h,e);return e_(b,e,d),i}}let e1={async create(a){let b=dN(),c=await dO(b);return{cookie:await eZ("pkceCodeVerifier",b,a),value:c}},use:e0("pkce","pkceCodeVerifier")},e2="encodedState",e3={async create(a,b){let{provider:c}=a;if(!c.checks.includes("state")){if(b)throw new x("State data was provided but the provider is not configured to use state");return}let d={origin:b,random:dN()},e=await bS({secret:a.jwt.secret,token:d,salt:e2,maxAge:900});return{cookie:await eZ("state",e,a),value:e}},use:e0("state","state"),async decode(a,b){try{b.logger.debug("DECODE_STATE",{state:a});let c=await bT({secret:b.jwt.secret,token:a,salt:e2});if(c)return c;throw Error("Invalid state")}catch(a){throw new x("State could not be decoded",{cause:a})}}},e4={async create(a){if(!a.provider.checks.includes("nonce"))return;let b=dN();return{cookie:await eZ("nonce",b,a),value:b}},use:e0("nonce","nonce")},e5="encodedWebauthnChallenge",e6={create:async(a,b,c)=>({cookie:await eZ("webauthnChallenge",await bS({secret:a.jwt.secret,token:{challenge:b,registerData:c},salt:e5,maxAge:900}),a)}),async use(a,b,c){let d=b?.[a.cookies.webauthnChallenge.name],e=await e$("webauthnChallenge",d,a),f=await bT({secret:a.jwt.secret,token:e,salt:e5});if(e_("webauthnChallenge",a,c),!f)throw new x("WebAuthn challenge was missing");return f}};function e7(a){return encodeURIComponent(a).replace(/%20/g,"+")}async function e8(a,b,c){let d,e,f,{logger:g,provider:h}=c,{token:i,userinfo:j}=h;if(i?.url&&"authjs.dev"!==i.url.host||j?.url&&"authjs.dev"!==j.url.host)d={issuer:h.issuer??"https://authjs.dev",token_endpoint:i?.url.toString(),userinfo_endpoint:j?.url.toString()};else{let a=new URL(h.issuer),b=await dI(a,{[dq]:!0,[dt]:h[cc]});if(!(d=await dL(a,b)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!d.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let k={client_id:h.clientId,...h.client};switch(k.token_endpoint_auth_method){case void 0:case"client_secret_basic":e=(a,b,c,d)=>{d.set("authorization",function(a,b){let c=e7(a),d=e7(b),e=btoa(`${c}:${d}`);return`Basic ${e}`}(h.clientId,h.clientSecret))};break;case"client_secret_post":var l;dK(l=h.clientSecret,'"clientSecret"'),e=(a,b,c,d)=>{c.set("client_id",b.client_id),c.set("client_secret",l)};break;case"client_secret_jwt":e=function(a,b){let c;dK(a,'"clientSecret"');let d=void 0;return async(b,e,f,g)=>{c||=await crypto.subtle.importKey("raw",dy(a),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let h={alg:"HS256"},i=dU(b,e);d?.(h,i);let j=`${dz(dy(JSON.stringify(h)))}.${dz(dy(JSON.stringify(i)))}`,k=await crypto.subtle.sign(c.algorithm,c,dy(j));f.set("client_id",e.client_id),f.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),f.set("client_assertion",`${j}.${dz(new Uint8Array(k))}`)}}(h.clientSecret);break;case"private_key_jwt":e=function(a,b){let{key:c,kid:d}=a instanceof CryptoKey?{key:a}:a?.key instanceof CryptoKey?(void 0!==a.kid&&dK(a.kid,'"kid"'),{key:a.key,kid:a.kid}):{};var e='"clientPrivateKey.key"';if(!(c instanceof CryptoKey))throw dp(`${e} must be a CryptoKey`,dn);if("private"!==c.type)throw dp(`${e} must be a private CryptoKey`,dm);return async(a,e,f,g)=>{let h={alg:function(a){switch(a.algorithm.name){case"RSA-PSS":switch(a.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new dA("unsupported RsaHashedKeyAlgorithm hash name",{cause:a})}case"RSASSA-PKCS1-v1_5":switch(a.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new dA("unsupported RsaHashedKeyAlgorithm hash name",{cause:a})}case"ECDSA":switch(a.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new dA("unsupported EcKeyAlgorithm namedCurve",{cause:a})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new dA("unsupported CryptoKey algorithm name",{cause:a})}}(c),kid:d},i=dU(a,e);b?.[du]?.(h,i),f.set("client_id",e.client_id),f.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),f.set("client_assertion",await dV(h,i,c))}}(h.token.clientPrivateKey,{[du](a,b){b.aud=[d.issuer,d.token_endpoint]}});break;case"none":e=(a,b,c,d)=>{c.set("client_id",b.client_id)};break;default:throw Error("unsupported client authentication method")}let m=[],n=await e3.use(b,m,c);try{f=function(a,b,c,d){var e;if(dS(a),dT(b),c instanceof URL&&(c=c.searchParams),!(c instanceof URLSearchParams))throw dp('"parameters" must be an instance of URLSearchParams, or URL',dn);if(eT(c,"response"))throw dC('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',eF,{parameters:c});let f=eT(c,"iss"),g=eT(c,"state");if(!f&&a.authorization_response_iss_parameter_supported)throw dC('response parameter "iss" (issuer) missing',eF,{parameters:c});if(f&&f!==a.issuer)throw dC('unexpected "iss" (issuer) response parameter value',eF,{expected:a.issuer,parameters:c});switch(d){case void 0:case eV:if(void 0!==g)throw dC('unexpected "state" response parameter encountered',eF,{expected:void 0,parameters:c});break;case eU:break;default:if(dK(d,'"expectedState" argument'),g!==d)throw dC(void 0===g?'response parameter "state" missing':'unexpected "state" response parameter value',eF,{expected:d,parameters:c})}if(eT(c,"error"))throw new d_("authorization response from the server is an error",{cause:c});let h=eT(c,"id_token"),i=eT(c,"token");if(void 0!==h||void 0!==i)throw new dA("implicit and hybrid flows are not supported");return e=new URLSearchParams(c),ep.add(e),e}(d,k,new URLSearchParams(a),h.checks.includes("state")?n:eU)}catch(a){if(a instanceof d_){let b={providerId:h.id,...Object.fromEntries(a.cause.entries())};throw g.debug("OAuthCallbackError",b),new E("OAuth Provider returned an error",b)}throw a}let o=await e1.use(b,m,c),p=h.callbackUrl;!c.isOnRedirectProxy&&h.redirectProxyUrl&&(p=h.redirectProxyUrl);let q=await er(d,k,e,f,p,o??"decoy",{[dq]:!0,[dt]:(...a)=>(h.checks.includes("pkce")||a[1].body.delete("code_verifier"),(h[cc]??fetch)(...a))});h.token?.conform&&(q=await h.token.conform(q.clone())??q);let r={},s="oidc"===h.type;if(h[cd])switch(h.id){case"microsoft-entra-id":case"azure-ad":{let a=await q.clone().json();if(a.error){let b={providerId:h.id,...a};throw new E(`OAuth Provider returned an error: ${a.error}`,b)}let{tid:b}=function(a){let b,c;if("string"!=typeof a)throw new aw("JWTs must use Compact JWS serialization, JWT must be a string");let{1:d,length:e}=a.split(".");if(5===e)throw new aw("Only JWTs using Compact JWS serialization can be decoded");if(3!==e)throw new aw("Invalid JWT");if(!d)throw new aw("JWTs must contain a payload");try{b=an(d)}catch{throw new aw("Failed to base64url decode the payload")}try{c=JSON.parse(ai.decode(b))}catch{throw new aw("Failed to parse the decoded payload as JSON")}if(!aD(c))throw new aw("Invalid JWT Claims Set");return c}(a.id_token);if("string"==typeof b){let a=d.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",c=new URL(d.issuer.replace(a,b)),e=await dI(c,{[dt]:h[cc]});d=await dL(c,e)}}}let t=await ew(d,k,q,{expectedNonce:await e4.use(b,m,c),requireIdToken:s});if(s){let b=ei(t);if(r=b,h[cd]&&"apple"===h.id)try{r.user=JSON.parse(a?.user)}catch{}if(!1===h.idToken){let a=await ea(d,k,t.access_token,{[dt]:h[cc],[dq]:!0});r=await ed(d,k,b.sub,a)}}else if(j?.request){let a=await j.request({tokens:t,provider:h});a instanceof Object&&(r=a)}else if(j?.url){let a=await ea(d,k,t.access_token,{[dt]:h[cc],[dq]:!0});r=await a.json()}else throw TypeError("No userinfo endpoint configured");return t.expires_in&&(t.expires_at=Math.floor(Date.now()/1e3)+Number(t.expires_in)),{...await e9(r,h,t,g),profile:r,cookies:m}}async function e9(a,b,c,d){try{let d=await b.profile(a,c);return{user:{...d,id:crypto.randomUUID(),email:d.email?.toLowerCase()},account:{...c,provider:b.id,type:b.type,providerAccountId:d.id??crypto.randomUUID()}}}catch(c){d.debug("getProfile error details",a),d.error(new F(c,{provider:b.id}))}}async function fa(a,b,c,d){let e=await ff(a,b,c),{cookie:f}=await e6.create(a,e.challenge,c);return{status:200,cookies:[...d??[],f],body:{action:"register",options:e},headers:{"Content-Type":"application/json"}}}async function fb(a,b,c,d){let e=await fe(a,b,c),{cookie:f}=await e6.create(a,e.challenge);return{status:200,cookies:[...d??[],f],body:{action:"authenticate",options:e},headers:{"Content-Type":"application/json"}}}async function fc(a,b,c){let d,{adapter:e,provider:f}=a,g=b.body&&"string"==typeof b.body.data?JSON.parse(b.body.data):void 0;if(!g||"object"!=typeof g||!("id"in g)||"string"!=typeof g.id)throw new n("Invalid WebAuthn Authentication response");let h=fi(fh(g.id)),i=await e.getAuthenticator(h);if(!i)throw new n(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:h})}`);let{challenge:j}=await e6.use(a,b.cookies,c);try{var k;let c=f.getRelayingParty(a,b);d=await f.simpleWebAuthn.verifyAuthenticationResponse({...f.verifyAuthenticationOptions,expectedChallenge:j,response:g,authenticator:{...k=i,credentialDeviceType:k.credentialDeviceType,transports:fj(k.transports),credentialID:fh(k.credentialID),credentialPublicKey:fh(k.credentialPublicKey)},expectedOrigin:c.origin,expectedRPID:c.id})}catch(a){throw new T(a)}let{verified:l,authenticationInfo:m}=d;if(!l)throw new T("WebAuthn authentication response could not be verified");try{let{newCounter:a}=m;await e.updateAuthenticatorCounter(i.credentialID,a)}catch(a){throw new p(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:h,oldCounter:i.counter,newCounter:m.newCounter})}`,a)}let o=await e.getAccount(i.providerAccountId,f.id);if(!o)throw new n(`WebAuthn account not found in database: ${JSON.stringify({credentialID:h,providerAccountId:i.providerAccountId})}`);let q=await e.getUser(o.userId);if(!q)throw new n(`WebAuthn user not found in database: ${JSON.stringify({credentialID:h,providerAccountId:i.providerAccountId,userID:o.userId})}`);return{account:o,user:q}}async function fd(a,b,c){var d;let e,{provider:f}=a,g=b.body&&"string"==typeof b.body.data?JSON.parse(b.body.data):void 0;if(!g||"object"!=typeof g||!("id"in g)||"string"!=typeof g.id)throw new n("Invalid WebAuthn Registration response");let{challenge:h,registerData:i}=await e6.use(a,b.cookies,c);if(!i)throw new n("Missing user registration data in WebAuthn challenge cookie");try{let c=f.getRelayingParty(a,b);e=await f.simpleWebAuthn.verifyRegistrationResponse({...f.verifyRegistrationOptions,expectedChallenge:h,response:g,expectedOrigin:c.origin,expectedRPID:c.id})}catch(a){throw new T(a)}if(!e.verified||!e.registrationInfo)throw new T("WebAuthn registration response could not be verified");let j={providerAccountId:fi(e.registrationInfo.credentialID),provider:a.provider.id,type:f.type},k={providerAccountId:j.providerAccountId,counter:e.registrationInfo.counter,credentialID:fi(e.registrationInfo.credentialID),credentialPublicKey:fi(e.registrationInfo.credentialPublicKey),credentialBackedUp:e.registrationInfo.credentialBackedUp,credentialDeviceType:e.registrationInfo.credentialDeviceType,transports:(d=g.response.transports,d?.join(","))};return{user:i,account:j,authenticator:k}}async function fe(a,b,c){let{provider:d,adapter:e}=a,f=c&&c.id?await e.listAuthenticatorsByUserId(c.id):null,g=d.getRelayingParty(a,b);return await d.simpleWebAuthn.generateAuthenticationOptions({...d.authenticationOptions,rpID:g.id,allowCredentials:f?.map(a=>({id:fh(a.credentialID),type:"public-key",transports:fj(a.transports)}))})}async function ff(a,b,c){let{provider:d,adapter:e}=a,f=c.id?await e.listAuthenticatorsByUserId(c.id):null,g=b5(32),h=d.getRelayingParty(a,b);return await d.simpleWebAuthn.generateRegistrationOptions({...d.registrationOptions,userID:g,userName:c.email,userDisplayName:c.name??void 0,rpID:h.id,rpName:h.name,excludeCredentials:f?.map(a=>({id:fh(a.credentialID),type:"public-key",transports:fj(a.transports)}))})}function fg(a){let{provider:b,adapter:c}=a;if(!c)throw new z("An adapter is required for the WebAuthn provider");if(!b||"webauthn"!==b.type)throw new M("Provider must be WebAuthn");return{...a,provider:b,adapter:c}}function fh(a){return new Uint8Array(Buffer.from(a,"base64"))}function fi(a){return Buffer.from(a).toString("base64")}function fj(a){return a?a.split(","):void 0}async function fk(a,b,c,d){if(!b.provider)throw new M("Callback route called without provider");let{query:e,body:f,method:g,headers:h}=a,{provider:i,adapter:j,url:k,callbackUrl:l,pages:m,jwt:o,events:p,callbacks:q,session:{strategy:s,maxAge:t},logger:u}=b,w="jwt"===s;try{if("oauth"===i.type||"oidc"===i.type){let g,h=i.authorization?.url.searchParams.get("response_mode")==="form_post"?f:e;if(b.isOnRedirectProxy&&h?.state){let a=await e3.decode(h.state,b);if(a?.origin&&new URL(a.origin).origin!==b.url.origin){let b=`${a.origin}?${new URLSearchParams(h)}`;return u.debug("Proxy redirecting to",b),{redirect:b,cookies:d}}}let n=await e8(h,a.cookies,b);n.cookies.length&&d.push(...n.cookies),u.debug("authorization result",n);let{user:r,account:s,profile:v}=n;if(!r||!s||!v)return{redirect:`${k}/signin`,cookies:d};if(j){let{getUserByAccount:a}=j;g=await a({providerAccountId:s.providerAccountId,provider:i.id})}let x=await fl({user:g??r,account:s,profile:v},b);if(x)return{redirect:x,cookies:d};let{user:y,session:z,isNewUser:A}=await dk(c.value,r,s,b);if(w){let a={name:y.name,email:y.email,picture:y.image,sub:y.id?.toString()},e=await q.jwt({token:a,user:y,account:s,profile:v,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await o.encode({...o,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*t);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:z.sessionToken,options:{...b.cookies.sessionToken.options,expires:z.expires}});if(await p.signIn?.({user:y,account:s,profile:v,isNewUser:A}),A&&m.newUser)return{redirect:`${m.newUser}${m.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:l})}`,cookies:d};return{redirect:l,cookies:d}}if("email"===i.type){let a=e?.token,f=e?.email;if(!a){let b=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!a}});throw b.name="Configuration",b}let g=i.secret??b.secret,h=await j.useVerificationToken({identifier:f,token:await b4(`${a}${g}`)}),k=!!h,n=k&&h.expires.valueOf()<Date.now();if(!k||n||f&&h.identifier!==f)throw new O({hasInvite:k,expired:n});let{identifier:r}=h,s=await j.getUserByEmail(r)??{id:crypto.randomUUID(),email:r,emailVerified:null},u={providerAccountId:s.email,userId:s.id,type:"email",provider:i.id},v=await fl({user:s,account:u},b);if(v)return{redirect:v,cookies:d};let{user:x,session:y,isNewUser:z}=await dk(c.value,s,u,b);if(w){let a={name:x.name,email:x.email,picture:x.image,sub:x.id?.toString()},e=await q.jwt({token:a,user:x,account:u,isNewUser:z,trigger:z?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await o.encode({...o,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*t);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:y.sessionToken,options:{...b.cookies.sessionToken.options,expires:y.expires}});if(await p.signIn?.({user:x,account:u,isNewUser:z}),z&&m.newUser)return{redirect:`${m.newUser}${m.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:l})}`,cookies:d};return{redirect:l,cookies:d}}if("credentials"===i.type&&"POST"===g){let a=f??{};Object.entries(e??{}).forEach(([a,b])=>k.searchParams.set(a,b));let j=await i.authorize(a,new Request(k,{headers:h,method:g,body:JSON.stringify(f)}));if(j)j.id=j.id?.toString()??crypto.randomUUID();else throw new v;let m={providerAccountId:j.id,type:"credentials",provider:i.id},n=await fl({user:j,account:m,credentials:a},b);if(n)return{redirect:n,cookies:d};let r={name:j.name,email:j.email,picture:j.image,sub:j.id},s=await q.jwt({token:r,user:j,account:m,isNewUser:!1,trigger:"signIn"});if(null===s)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,e=await o.encode({...o,token:s,salt:a}),f=new Date;f.setTime(f.getTime()+1e3*t);let g=c.chunk(e,{expires:f});d.push(...g)}return await p.signIn?.({user:j,account:m}),{redirect:l,cookies:d}}else if("webauthn"===i.type&&"POST"===g){let e,f,g,h=a.body?.action;if("string"!=typeof h||"authenticate"!==h&&"register"!==h)throw new n("Invalid action parameter");let i=fg(b);switch(h){case"authenticate":{let b=await fc(i,a,d);e=b.user,f=b.account;break}case"register":{let c=await fd(b,a,d);e=c.user,f=c.account,g=c.authenticator}}await fl({user:e,account:f},b);let{user:j,isNewUser:k,session:r,account:s}=await dk(c.value,e,f,b);if(!s)throw new n("Error creating or finding account");if(g&&j.id&&await i.adapter.createAuthenticator({...g,userId:j.id}),w){let a={name:j.name,email:j.email,picture:j.image,sub:j.id?.toString()},e=await q.jwt({token:a,user:j,account:s,isNewUser:k,trigger:k?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await o.encode({...o,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*t);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:r.sessionToken,options:{...b.cookies.sessionToken.options,expires:r.expires}});if(await p.signIn?.({user:j,account:s,isNewUser:k}),k&&m.newUser)return{redirect:`${m.newUser}${m.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:l})}`,cookies:d};return{redirect:l,cookies:d}}throw new M(`Callback for provider type (${i.type}) is not supported`)}catch(b){if(b instanceof n)throw b;let a=new r(b,{provider:i.id});throw u.debug("callback route error details",{method:g,query:e,body:f}),a}}async function fl(a,b){let c,{signIn:d,redirect:e}=b.callbacks;try{c=await d(a)}catch(a){if(a instanceof n)throw a;throw new q(a)}if(!c)throw new q("AccessDenied");if("string"==typeof c)return await e({url:c,baseUrl:b.url.origin})}async function fm(a,b,c,d,e){let{adapter:f,jwt:g,events:h,callbacks:i,logger:j,session:{strategy:k,maxAge:l}}=a,m={body:null,headers:{"Content-Type":"application/json",...!d&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:c},n=b.value;if(!n)return m;if("jwt"===k){try{let c=a.cookies.sessionToken.name,f=await g.decode({...g,token:n,salt:c});if(!f)throw Error("Invalid JWT");let j=await i.jwt({token:f,...d&&{trigger:"update"},session:e}),k=dj(l);if(null!==j){let a={user:{name:j.name,email:j.email,image:j.picture},expires:k.toISOString()},d=await i.session({session:a,token:j});m.body=d;let e=await g.encode({...g,token:j,salt:c}),f=b.chunk(e,{expires:k});m.cookies?.push(...f),await h.session?.({session:d,token:j})}else m.cookies?.push(...b.clean())}catch(a){j.error(new y(a)),m.cookies?.push(...b.clean())}return m}try{let{getSessionAndUser:c,deleteSession:g,updateSession:j}=f,k=await c(n);if(k&&k.session.expires.valueOf()<Date.now()&&(await g(n),k=null),k){let{user:b,session:c}=k,f=a.session.updateAge,g=c.expires.valueOf()-1e3*l+1e3*f,o=dj(l);g<=Date.now()&&await j({sessionToken:n,expires:o});let p=await i.session({session:{...c,user:b},user:b,newSession:e,...d?{trigger:"update"}:{}});m.body=p,m.cookies?.push({name:a.cookies.sessionToken.name,value:n,options:{...a.cookies.sessionToken.options,expires:o}}),await h.session?.({session:p})}else n&&m.cookies?.push(...b.clean())}catch(a){j.error(new G(a))}return m}async function fn(a,b){let c,d,{logger:e,provider:f}=b,g=f.authorization?.url;if(!g||"authjs.dev"===g.host){let a=new URL(f.issuer),b=await dI(a,{[dt]:f[cc],[dq]:!0}),c=await dL(a,b).catch(b=>{if(!(b instanceof TypeError)||"Invalid URL"!==b.message)throw b;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${a}`)});if(!c.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");g=new URL(c.authorization_endpoint)}let h=g.searchParams,i=f.callbackUrl;!b.isOnRedirectProxy&&f.redirectProxyUrl&&(i=f.redirectProxyUrl,d=f.callbackUrl,e.debug("using redirect proxy",{redirect_uri:i,data:d}));let j=Object.assign({response_type:"code",client_id:f.clientId,redirect_uri:i,...f.authorization?.params},Object.fromEntries(f.authorization?.url.searchParams??[]),a);for(let a in j)h.set(a,j[a]);let k=[];f.authorization?.url.searchParams.get("response_mode")==="form_post"&&(b.cookies.state.options.sameSite="none",b.cookies.state.options.secure=!0,b.cookies.nonce.options.sameSite="none",b.cookies.nonce.options.secure=!0);let l=await e3.create(b,d);if(l&&(h.set("state",l.value),k.push(l.cookie)),f.checks?.includes("pkce"))if(c&&!c.code_challenge_methods_supported?.includes("S256"))"oidc"===f.type&&(f.checks=["nonce"]);else{let{value:a,cookie:c}=await e1.create(b);h.set("code_challenge",a),h.set("code_challenge_method","S256"),k.push(c)}let m=await e4.create(b);return m&&(h.set("nonce",m.value),k.push(m.cookie)),"oidc"!==f.type||g.searchParams.has("scope")||g.searchParams.set("scope","openid profile email"),e.debug("authorization url is ready",{url:g,cookies:k,provider:f}),{redirect:g.toString(),cookies:k}}async function fo(a,b){let c,{body:d}=a,{provider:e,callbacks:f,adapter:g}=b,h=(e.normalizeIdentifier??function(a){if(!a)throw Error("Missing email from request body.");let[b,c]=a.toLowerCase().trim().split("@");return c=c.split(",")[0],`${b}@${c}`})(d?.email),i={id:crypto.randomUUID(),email:h,emailVerified:null},j=await g.getUserByEmail(h)??i,k={providerAccountId:h,userId:j.id,type:"email",provider:e.id};try{c=await f.signIn({user:j,account:k,email:{verificationRequest:!0}})}catch(a){throw new q(a)}if(!c)throw new q("AccessDenied");if("string"==typeof c)return{redirect:await f.redirect({url:c,baseUrl:b.url.origin})};let{callbackUrl:l,theme:m}=b,n=await e.generateVerificationToken?.()??b5(32),o=new Date(Date.now()+(e.maxAge??86400)*1e3),p=e.secret??b.secret,r=new URL(b.basePath,b.url.origin),s=e.sendVerificationRequest({identifier:h,token:n,expires:o,url:`${r}/callback/${e.id}?${new URLSearchParams({callbackUrl:l,token:n,email:h})}`,provider:e,theme:m,request:new Request(a.url,{headers:a.headers,method:a.method,body:"POST"===a.method?JSON.stringify(a.body??{}):void 0})}),t=g.createVerificationToken?.({identifier:h,token:await b4(`${n}${p}`),expires:o});return await Promise.all([s,t]),{redirect:`${r}/verify-request?${new URLSearchParams({provider:e.id,type:e.type})}`}}async function fp(a,b,c){let d=`${c.url.origin}${c.basePath}/signin`;if(!c.provider)return{redirect:d,cookies:b};switch(c.provider.type){case"oauth":case"oidc":{let{redirect:d,cookies:e}=await fn(a.query,c);return e&&b.push(...e),{redirect:d,cookies:b}}case"email":return{...await fo(a,c),cookies:b};default:return{redirect:d,cookies:b}}}async function fq(a,b,c){let{jwt:d,events:e,callbackUrl:f,logger:g,session:h}=c,i=b.value;if(!i)return{redirect:f,cookies:a};try{if("jwt"===h.strategy){let a=c.cookies.sessionToken.name,b=await d.decode({...d,token:i,salt:a});await e.signOut?.({token:b})}else{let a=await c.adapter?.deleteSession(i);await e.signOut?.({session:a})}}catch(a){g.error(new J(a))}return a.push(...b.clean()),{redirect:f,cookies:a}}async function fr(a,b){let{adapter:c,jwt:d,session:{strategy:e}}=a,f=b.value;if(!f)return null;if("jwt"===e){let b=a.cookies.sessionToken.name,c=await d.decode({...d,token:f,salt:b});if(c&&c.sub)return{id:c.sub,name:c.name,email:c.email,image:c.picture}}else{let a=await c?.getSessionAndUser(f);if(a)return a.user}return null}async function fs(a,b,c,d){let e=fg(b),{provider:f}=e,{action:g}=a.query??{};if("register"!==g&&"authenticate"!==g&&void 0!==g)return{status:400,body:{error:"Invalid action"},cookies:d,headers:{"Content-Type":"application/json"}};let h=await fr(b,c),i=h?{user:h,exists:!0}:await f.getUserInfo(b,a),j=i?.user;switch(function(a,b,c){let{user:d,exists:e=!1}=c??{};switch(a){case"authenticate":return"authenticate";case"register":if(d&&b===e)return"register";break;case void 0:if(!b)if(!d)return"authenticate";else if(e)return"authenticate";else return"register"}return null}(g,!!h,i)){case"authenticate":return fb(e,a,j,d);case"register":if("string"==typeof j?.email)return fa(e,a,j,d);break;default:return{status:400,body:{error:"Invalid request"},cookies:d,headers:{"Content-Type":"application/json"}}}}async function ft(a,b){let{action:c,providerId:d,error:e,method:f}=a,g=b.skipCSRFCheck===ca,{options:h,cookies:i}=await cj({authOptions:b,action:c,providerId:d,url:a.url,callbackUrl:a.body?.callbackUrl??a.query?.callbackUrl,csrfToken:a.body?.csrfToken,cookies:a.cookies,isPost:"POST"===f,csrfDisabled:g}),j=new m(h.cookies.sessionToken,a.cookies,h.logger);if("GET"===f){let b=di({...h,query:a.query,cookies:i});switch(c){case"callback":return await fk(a,h,j,i);case"csrf":return b.csrf(g,h,i);case"error":return b.error(e);case"providers":return b.providers(h.providers);case"session":return await fm(h,j,i);case"signin":return b.signin(d,e);case"signout":return b.signout();case"verify-request":return b.verifyRequest();case"webauthn-options":return await fs(a,h,j,i)}}else{let{csrfTokenVerified:b}=h;switch(c){case"callback":return"credentials"===h.provider.type&&b7(c,b),await fk(a,h,j,i);case"session":return b7(c,b),await fm(h,j,i,!0,a.body?.data);case"signin":return b7(c,b),await fp(a,i,h);case"signout":return b7(c,b),await fq(i,j,h)}}throw new K(`Cannot handle action: ${c}`)}function fu(a,b,c,d,e){let f,g=e?.basePath,h=d.AUTH_URL??d.NEXTAUTH_URL;if(h)f=new URL(h),g&&"/"!==g&&"/"!==f.pathname&&(f.pathname!==g&&bZ(e).warn("env-url-basepath-mismatch"),f.pathname="/");else{let a=c.get("x-forwarded-host")??c.get("host"),d=c.get("x-forwarded-proto")??b??"https",e=d.endsWith(":")?d:d+":";f=new URL(`${e}//${a}`)}let i=f.toString().replace(/\/$/,"");if(g){let b=g?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${i}/${b}/${a}`)}return new URL(`${i}/${a}`)}async function fv(a,b){let c=bZ(b),d=await b2(a,b);if(!d)return Response.json("Bad request.",{status:400});let e=function(a,b){let{url:c}=a,d=[];if(!W&&b.debug&&d.push("debug-enabled"),!b.trustHost)return new N(`Host must be trusted. URL was: ${a.url}`);if(!b.secret?.length)return new C("Please define a `secret`");let e=a.query?.callbackUrl;if(e&&!X(e,c.origin))return new u(`Invalid callback URL. Received: ${e}`);let{callbackUrl:f}=l(b.useSecureCookies??"https:"===c.protocol),g=a.cookies?.[b.cookies?.callbackUrl?.name??f.name];if(g&&!X(g,c.origin))return new u(`Invalid callback URL. Received: ${g}`);let h=!1;for(let a of b.providers){let b="function"==typeof a?a():a;if(("oauth"===b.type||"oidc"===b.type)&&!(b.issuer??b.options?.issuer)){let a,{authorization:c,token:d,userinfo:e}=b;if("string"==typeof c||c?.url?"string"==typeof d||d?.url?"string"==typeof e||e?.url||(a="userinfo"):a="token":a="authorization",a)return new w(`Provider "${b.id}" is missing both \`issuer\` and \`${a}\` endpoint config. At least one of them is required`)}if("credentials"===b.type)Y=!0;else if("email"===b.type)Z=!0;else if("webauthn"===b.type){var i;if($=!0,b.simpleWebAuthnBrowserVersion&&(i=b.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(i)))return new n(`Invalid provider config for "${b.id}": simpleWebAuthnBrowserVersion "${b.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(b.enableConditionalUI){if(h)return new R("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(h=!0,!Object.values(b.formFields).some(a=>a.autocomplete&&a.autocomplete.toString().indexOf("webauthn")>-1))return new S(`Provider "${b.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(Y){let a=b.session?.strategy==="database",c=!b.providers.some(a=>"credentials"!==("function"==typeof a?a():a).type);if(a&&c)return new L("Signing in with credentials only supported if JWT strategy is enabled");if(b.providers.some(a=>{let b="function"==typeof a?a():a;return"credentials"===b.type&&!b.authorize}))return new B("Must define an authorize() handler to use credentials authentication provider")}let{adapter:j,session:k}=b,m=[];if(Z||k?.strategy==="database"||!k?.strategy&&j)if(Z){if(!j)return new z("Email login requires an adapter");m.push(..._)}else{if(!j)return new z("Database session requires an adapter");m.push(...aa)}if($){if(!b.experimental?.enableWebAuthn)return new V("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(d.push("experimental-webauthn"),!j)return new z("WebAuthn requires an adapter");m.push(...ab)}if(j){let a=m.filter(a=>!(a in j));if(a.length)return new A(`Required adapter methods were missing: ${a.join(", ")}`)}return W||(W=!0),d}(d,b);if(Array.isArray(e))e.forEach(c.warn);else if(e){if(c.error(e),!new Set(["signin","signout","error","verify-request"]).has(d.action)||"GET"!==d.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:a,theme:f}=b,g=a?.error&&d.url.searchParams.get("callbackUrl")?.startsWith(a.error);if(!a?.error||g)return g&&c.error(new s(`The error page ${a?.error} should not require authentication`)),b3(di({theme:f}).error("Configuration"));let h=`${d.url.origin}${a.error}?error=Configuration`;return Response.redirect(h)}let f=a.headers?.has("X-Auth-Return-Redirect"),g=b.raw===cb;try{let a=await ft(d,b);if(g)return a;let c=b3(a),e=c.headers.get("Location");if(!f||!e)return c;return Response.json({url:e},{headers:c.headers})}catch(l){c.error(l);let e=l instanceof n;if(e&&g&&!f)throw l;if("POST"===a.method&&"session"===d.action)return Response.json(null,{status:400});let h=new URLSearchParams({error:l instanceof n&&Q.has(l.type)?l.type:"Configuration"});l instanceof v&&h.set("code",l.code);let i=e&&l.kind||"error",j=b.pages?.[i]??`${b.basePath}/${i.toLowerCase()}`,k=`${d.url.origin}${j}?${h}`;if(f)return Response.json({url:k});return Response.redirect(k)}}var fw=c(32190);function fx(a){let b=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!b)return a;let{origin:c}=new URL(b),{href:d,origin:e}=a.nextUrl;return new fw.NextRequest(d.replace(e,c),a)}function fy(a){try{a.secret??(a.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let b=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!b)return;let{pathname:c}=new URL(b);if("/"===c)return;a.basePath||(a.basePath=c)}catch{}finally{a.basePath||(a.basePath="/api/auth"),function(a,b,c=!1){try{let d=a.AUTH_URL;d&&(b.basePath?c||bZ(b).warn("env-url-basepath-redundant"):b.basePath=new URL(d).pathname)}catch{}finally{b.basePath??(b.basePath="/auth")}if(!b.secret?.length){b.secret=[];let c=a.AUTH_SECRET;for(let d of(c&&b.secret.push(c),[1,2,3])){let c=a[`AUTH_SECRET_${d}`];c&&b.secret.unshift(c)}}b.redirectProxyUrl??(b.redirectProxyUrl=a.AUTH_REDIRECT_PROXY_URL),b.trustHost??(b.trustHost=!!(a.AUTH_URL??a.AUTH_TRUST_HOST??a.VERCEL??a.CF_PAGES??"production"!==a.NODE_ENV)),b.providers=b.providers.map(b=>{let{id:c}="function"==typeof b?b({}):b,d=c.toUpperCase().replace(/-/g,"_"),e=a[`AUTH_${d}_ID`],f=a[`AUTH_${d}_SECRET`],g=a[`AUTH_${d}_ISSUER`],h=a[`AUTH_${d}_KEY`],i="function"==typeof b?b({clientId:e,clientSecret:f,issuer:g,apiKey:h}):b;return"oauth"===i.type||"oidc"===i.type?(i.clientId??(i.clientId=e),i.clientSecret??(i.clientSecret=f),i.issuer??(i.issuer=g)):"email"===i.type&&(i.apiKey??(i.apiKey=h)),i})}(process.env,a,!0)}}var fz=c(99933),fA=c(86280);async function fB(a,b){return fv(new Request(fu("session",a.get("x-forwarded-proto"),a,process.env,b),{headers:{cookie:a.get("cookie")??""}}),{...b,callbacks:{...b.callbacks,async session(...a){let c=await b.callbacks?.session?.(...a)??{...a[0].session,expires:a[0].session.expires?.toISOString?.()??a[0].session.expires};return{user:a[0].user??a[0].token,...c}}}})}function fC(a){return"function"==typeof a}function fD(a,b){return"function"==typeof a?async(...c)=>{if(!c.length){let c=await (0,fA.b)(),d=await a(void 0);return b?.(d),fB(c,d).then(a=>a.json())}if(c[0]instanceof Request){let d=c[0],e=c[1],f=await a(d);return b?.(f),fE([d,e],f)}if(fC(c[0])){let d=c[0];return async(...c)=>{let e=await a(c[0]);return b?.(e),fE(c,e,d)}}let d="req"in c[0]?c[0].req:c[0],e="res"in c[0]?c[0].res:c[1],f=await a(d);return b?.(f),fB(new Headers(d.headers),f).then(async a=>{let b=await a.json();for(let b of a.headers.getSetCookie())"headers"in e?e.headers.append("set-cookie",b):e.appendHeader("set-cookie",b);return b})}:(...b)=>{if(!b.length)return Promise.resolve((0,fA.b)()).then(b=>fB(b,a).then(a=>a.json()));if(b[0]instanceof Request)return fE([b[0],b[1]],a);if(fC(b[0])){let c=b[0];return async(...b)=>fE(b,a,c).then(a=>a)}let c="req"in b[0]?b[0].req:b[0],d="res"in b[0]?b[0].res:b[1];return fB(new Headers(c.headers),a).then(async a=>{let b=await a.json();for(let b of a.headers.getSetCookie())"headers"in d?d.headers.append("set-cookie",b):d.appendHeader("set-cookie",b);return b})}}async function fE(a,b,c){let d=fx(a[0]),e=await fB(d.headers,b),f=await e.json(),g=!0;b.callbacks?.authorized&&(g=await b.callbacks.authorized({request:d,auth:f}));let h=fw.NextResponse.next?.();if(g instanceof Response){h=g;let a=g.headers.get("Location"),{pathname:c}=d.nextUrl;a&&function(a,b,c){let d=b.replace(`${a}/`,""),e=Object.values(c.pages??{});return(fF.has(d)||e.includes(b))&&b===a}(c,new URL(a).pathname,b)&&(g=!0)}else if(c)d.auth=f,h=await c(d,a[1])??fw.NextResponse.next();else if(!g){let a=b.pages?.signIn??`${b.basePath}/signin`;if(d.nextUrl.pathname!==a){let b=d.nextUrl.clone();b.pathname=a,b.searchParams.set("callbackUrl",d.nextUrl.href),h=fw.NextResponse.redirect(b)}}let i=new Response(h?.body,h);for(let a of e.headers.getSetCookie())i.headers.append("set-cookie",a);return i}c(73913);let fF=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var fG=c(39916);async function fH(a,b={},c,d){let e=new Headers(await (0,fA.b)()),{redirect:f=!0,redirectTo:g,...h}=b instanceof FormData?Object.fromEntries(b):b,i=g?.toString()??e.get("Referer")??"/",j=fu("signin",e.get("x-forwarded-proto"),e,process.env,d);if(!a)return j.searchParams.append("callbackUrl",i),f&&(0,fG.redirect)(j.toString()),j.toString();let k=`${j}/${a}?${new URLSearchParams(c)}`,l={};for(let b of d.providers){let{options:c,...d}="function"==typeof b?b():b,e=c?.id??d.id;if(e===a){l={id:e,type:c?.type??d.type};break}}if(!l.id){let a=`${j}?${new URLSearchParams({callbackUrl:i})}`;return f&&(0,fG.redirect)(a),a}"credentials"===l.type&&(k=k.replace("signin","callback")),e.set("Content-Type","application/x-www-form-urlencoded");let m=new Request(k,{method:"POST",headers:e,body:new URLSearchParams({...h,callbackUrl:i})}),n=await fv(m,{...d,raw:cb,skipCSRFCheck:ca}),o=await (0,fz.U)();for(let a of n?.cookies??[])o.set(a.name,a.value,a.options);let p=(n instanceof Response?n.headers.get("Location"):n.redirect)??k;return f?(0,fG.redirect)(p):p}async function fI(a,b){let c=new Headers(await (0,fA.b)());c.set("Content-Type","application/x-www-form-urlencoded");let d=fu("signout",c.get("x-forwarded-proto"),c,process.env,b),e=new URLSearchParams({callbackUrl:a?.redirectTo??c.get("Referer")??"/"}),f=new Request(d,{method:"POST",headers:c,body:e}),g=await fv(f,{...b,raw:cb,skipCSRFCheck:ca}),h=await (0,fz.U)();for(let a of g?.cookies??[])h.set(a.name,a.value,a.options);return a?.redirect??!0?(0,fG.redirect)(g.redirect):g}async function fJ(a,b){let c=new Headers(await (0,fA.b)());c.set("Content-Type","application/json");let d=new Request(fu("session",c.get("x-forwarded-proto"),c,process.env,b),{method:"POST",headers:c,body:JSON.stringify({data:a})}),e=await fv(d,{...b,raw:cb,skipCSRFCheck:ca}),f=await (0,fz.U)();for(let a of e?.cookies??[])f.set(a.name,a.value,a.options);return e.body}function fK(a){if("function"==typeof a){let b=async b=>{let c=await a(b);return fy(c),fv(fx(b),c)};return{handlers:{GET:b,POST:b},auth:fD(a,a=>fy(a)),signIn:async(b,c,d)=>{let e=await a(void 0);return fy(e),fH(b,c,d,e)},signOut:async b=>{let c=await a(void 0);return fy(c),fI(b,c)},unstable_update:async b=>{let c=await a(void 0);return fy(c),fJ(b,c)}}}fy(a);let b=b=>fv(fx(b),a);return{handlers:{GET:b,POST:b},auth:fD(a),signIn:(b,c,d)=>fH(b,c,d,a),signOut:b=>fI(b,a),unstable_update:b=>fJ(b,a)}}},39916:(a,b,c)=>{var d=c(97576);c.o(d,"redirect")&&c.d(b,{redirect:function(){return d.redirect}})},48976:(a,b,c)=>{function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"forbidden",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},62765:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"notFound",{enumerable:!0,get:function(){return e}});let d=""+c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},70899:(a,b,c)=>{function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unauthorized",{enumerable:!0,get:function(){return d}}),c(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},71042:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=c(68388),e=c(52637),f=c(51846),g=c(31162),h=c(84971),i=c(98479);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},73913:(a,b,c)=>{let d=c(63033),e=c(29294),f=c(84971),g=c(76926),h=c(80023),i=c(98479),j=c(71617);c(43763);new WeakMap;(0,g.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})})},86280:(a,b,c)=>{Object.defineProperty(b,"b",{enumerable:!0,get:function(){return m}});let d=c(92584),e=c(29294),f=c(63033),g=c(84971),h=c(80023),i=c(68388),j=c(76926);c(44523);let k=c(8719),l=c(71617);function m(){let a=e.workAsyncStorage.getStore(),b=f.workUnitAsyncStorage.getStore();if(a){if(b&&"after"===b.phase&&!(0,k.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(a.forceStatic)return o(d.HeadersAdapter.seal(new Headers({})));if(b){if("cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(a.dynamicShouldError)throw Object.defineProperty(new h.StaticGenBailoutError(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(b)switch(b.type){case"prerender":var c=b;let e=n.get(c);if(e)return e;let f=(0,i.makeHangingPromise)(c.renderSignal,"`headers()`");return n.set(c,f),f;case"prerender-client":let j="`headers`";throw Object.defineProperty(new l.InvariantError(`${j} must not be used within a client component. Next.js should be preventing ${j} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,g.postponeWithTracking)(a.route,"headers",b.dynamicTracking);break;case"prerender-legacy":(0,g.throwToInterruptStaticGeneration)("headers",a,b)}(0,g.trackDynamicDataInDynamicRender)(a,b)}return o((0,f.getExpectedRequestStore)("headers").headers)}c(43763);let n=new WeakMap;function o(a){let b=n.get(a);if(b)return b;let c=Promise.resolve(a);return n.set(a,c),Object.defineProperties(c,{append:{value:a.append.bind(a)},delete:{value:a.delete.bind(a)},get:{value:a.get.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},getSetCookie:{value:a.getSetCookie.bind(a)},forEach:{value:a.forEach.bind(a)},keys:{value:a.keys.bind(a)},values:{value:a.values.bind(a)},entries:{value:a.entries.bind(a)},[Symbol.iterator]:{value:a[Symbol.iterator].bind(a)}}),c}(0,j.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})})},86897:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=c(52836),e=c(49026),f=c(19121).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},94069:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{MutableRequestCookiesAdapter:function(){return m},ReadonlyRequestCookiesError:function(){return h},RequestCookiesAdapter:function(){return i},appendMutableCookies:function(){return l},areCookiesMutableInCurrentPhase:function(){return o},getModifiedCookieValues:function(){return k},responseCookiesToRequestCookies:function(){return q},wrapWithMutableAccessCheck:function(){return n}});let d=c(23158),e=c(43763),f=c(29294),g=c(63033);class h extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new h}}class i{static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"clear":case"delete":case"set":return h.callable;default:return e.ReflectAdapter.get(a,b,c)}}})}}let j=Symbol.for("next.mutated.cookies");function k(a){let b=a[j];return b&&Array.isArray(b)&&0!==b.length?b:[]}function l(a,b){let c=k(b);if(0===c.length)return!1;let e=new d.ResponseCookies(a),f=e.getAll();for(let a of c)e.set(a);for(let a of f)e.set(a);return!0}class m{static wrap(a,b){let c=new d.ResponseCookies(new Headers);for(let b of a.getAll())c.set(b);let g=[],h=new Set,i=()=>{let a=f.workAsyncStorage.getStore();if(a&&(a.pathWasRevalidated=!0),g=c.getAll().filter(a=>h.has(a.name)),b){let a=[];for(let b of g){let c=new d.ResponseCookies(new Headers);c.set(b),a.push(c.toString())}b(a)}},k=new Proxy(c,{get(a,b,c){switch(b){case j:return g;case"delete":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.delete(...b),k}finally{i()}};case"set":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.set(...b),k}finally{i()}};default:return e.ReflectAdapter.get(a,b,c)}}});return k}}function n(a){let b=new Proxy(a,{get(a,c,d){switch(c){case"delete":return function(...c){return p("cookies().delete"),a.delete(...c),b};case"set":return function(...c){return p("cookies().set"),a.set(...c),b};default:return e.ReflectAdapter.get(a,c,d)}}});return b}function o(a){return"action"===a.phase}function p(a){if(!o((0,g.getExpectedRequestStore)(a)))throw new h}function q(a){let b=new d.RequestCookies(new Headers);for(let c of a.getAll())b.set(c);return b}},97576:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_rethrow:function(){return i.unstable_rethrow}});let d=c(86897),e=c(49026),f=c(62765),g=c(48976),h=c(70899),i=c(163);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},99933:(a,b,c)=>{Object.defineProperty(b,"U",{enumerable:!0,get:function(){return n}});let d=c(94069),e=c(23158),f=c(29294),g=c(63033),h=c(84971),i=c(80023),j=c(68388),k=c(76926);c(44523);let l=c(8719),m=c(71617);function n(){let a="cookies",b=f.workAsyncStorage.getStore(),c=g.workUnitAsyncStorage.getStore();if(b){if(c&&"after"===c.phase&&!(0,l.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(b.forceStatic)return p(d.RequestCookiesAdapter.seal(new e.RequestCookies(new Headers({}))));if(c){if("cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===c.type)throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(b.dynamicShouldError)throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${b.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(c)switch(c.type){case"prerender":var k=c;let f=o.get(k);if(f)return f;let g=(0,j.makeHangingPromise)(k.renderSignal,"`cookies()`");return o.set(k,g),g;case"prerender-client":let n="`cookies`";throw Object.defineProperty(new m.InvariantError(`${n} must not be used within a client component. Next.js should be preventing ${n} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,h.postponeWithTracking)(b.route,a,c.dynamicTracking);break;case"prerender-legacy":(0,h.throwToInterruptStaticGeneration)(a,b,c)}(0,h.trackDynamicDataInDynamicRender)(b,c)}let n=(0,g.getExpectedRequestStore)(a);return p((0,d.areCookiesMutableInCurrentPhase)(n)?n.userspaceMutableCookies:n.cookies)}c(43763);let o=new WeakMap;function p(a){let b=o.get(a);if(b)return b;let c=Promise.resolve(a);return o.set(a,c),Object.defineProperties(c,{[Symbol.iterator]:{value:a[Symbol.iterator]?a[Symbol.iterator].bind(a):q.bind(a)},size:{get:()=>a.size},get:{value:a.get.bind(a)},getAll:{value:a.getAll.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},delete:{value:a.delete.bind(a)},clear:{value:"function"==typeof a.clear?a.clear.bind(a):r.bind(a,c)},toString:{value:a.toString.bind(a)}}),c}function q(){return this.getAll().map(a=>[a.name,a]).values()}function r(a){for(let a of this.getAll())this.delete(a.name);return a}(0,k.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})})}};