import { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, QrCode, Camera, CheckCircle, XCircle } from "lucide-react"

export const metadata: Metadata = {
  title: "QR Scanner | QRSAMS",
  description: "Scan student QR codes for attendance",
}

// Mock recent scans
const recentScans = [
  {
    id: "STU001",
    name: "<PERSON>",
    time: "8:15:23 AM",
    status: "success",
    action: "Check In",
  },
  {
    id: "STU002",
    name: "<PERSON>",
    time: "8:12:45 AM", 
    status: "success",
    action: "Check In",
  },
  {
    id: "STU003",
    name: "<PERSON>",
    time: "8:10:12 AM",
    status: "success", 
    action: "Check In",
  },
]

export default function ScannerPage() {
  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="icon" asChild>
              <Link href="/dashboard">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">QR Scanner</h1>
              <p className="text-muted-foreground">
                Scan student QR codes for attendance tracking
              </p>
            </div>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Scanner Section */}
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <QrCode className="h-5 w-5" />
                <span>QR Code Scanner</span>
              </CardTitle>
              <CardDescription>
                Position the QR code within the camera frame
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="aspect-square bg-muted rounded-lg flex items-center justify-center border-2 border-dashed">
                <div className="text-center space-y-4">
                  <Camera className="h-16 w-16 mx-auto text-muted-foreground" />
                  <div>
                    <p className="text-lg font-medium">Camera View</p>
                    <p className="text-sm text-muted-foreground">
                      QR scanner will be displayed here
                    </p>
                  </div>
                  <Button>
                    <Camera className="mr-2 h-4 w-4" />
                    Start Camera
                  </Button>
                </div>
              </div>
              
              <div className="mt-4 space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Scanner Status:</span>
                  <Badge variant="secondary">Ready</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Scans Today:</span>
                  <span className="font-medium">1,180</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Last Scan:</span>
                  <span className="font-medium">8:15:23 AM</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Scans */}
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle>Recent Scans</CardTitle>
              <CardDescription>
                Latest QR code scan results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentScans.map((scan, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {scan.status === "success" ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                      <div>
                        <p className="font-medium">{scan.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {scan.id} • {scan.time}
                        </p>
                      </div>
                    </div>
                    <Badge 
                      variant={scan.status === "success" ? "secondary" : "destructive"}
                    >
                      {scan.action}
                    </Badge>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 pt-4 border-t">
                <Button variant="outline" className="w-full">
                  View All Scans
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Scans</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,180</div>
              <p className="text-xs text-muted-foreground">
                Today's total
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">99.8%</div>
              <p className="text-xs text-muted-foreground">
                Successful scans
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Scan Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1.2s</div>
              <p className="text-xs text-muted-foreground">
                Per scan
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Queue Length</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">
                Students waiting
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
