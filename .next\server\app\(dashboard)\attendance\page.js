(()=>{var a={};a.id=16,a.ids=[16],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3582:(a,b,c)=>{"use strict";c.d(b,{Table:()=>e,TableBody:()=>g,TableCell:()=>j,TableHead:()=>h,TableHeader:()=>f,TableRow:()=>i});var d=c(61369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\table.tsx","Table"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\table.tsx","TableHeader"),g=(0,d.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\table.tsx","TableBody");(0,d.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\table.tsx","TableFooter");let h=(0,d.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\table.tsx","TableHead"),i=(0,d.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\table.tsx","TableRow"),j=(0,d.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\table.tsx","TableCell");(0,d.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\table.tsx","TableCaption")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17549:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("qr-code",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},18112:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["attendance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,92995)),"C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\attendance\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,71934)),"C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Desktop\\qrsams\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,46055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\qrsams\\app\\(dashboard)\\attendance\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/attendance/page",pathname:"/attendance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/attendance/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24995:(a,b,c)=>{Promise.resolve().then(c.bind(c,96752)),Promise.resolve().then(c.bind(c,26269))},26269:(a,b,c)=>{"use strict";c.d(b,{Tabs:()=>D,TabsContent:()=>G,TabsList:()=>E,TabsTrigger:()=>F});var d=c(60687),e=c(43210),f=c(70569),g=c(11273),h=c(72942),i=c(46059),j=c(14163),k=c(43),l=c(65551),m=c(96963),n="Tabs",[o,p]=(0,g.A)(n,[h.RG]),q=(0,h.RG)(),[r,s]=o(n),t=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,onValueChange:f,defaultValue:g,orientation:h="horizontal",dir:i,activationMode:o="automatic",...p}=a,q=(0,k.jH)(i),[s,t]=(0,l.i)({prop:e,onChange:f,defaultProp:g??"",caller:n});return(0,d.jsx)(r,{scope:c,baseId:(0,m.B)(),value:s,onValueChange:t,orientation:h,dir:q,activationMode:o,children:(0,d.jsx)(j.sG.div,{dir:q,"data-orientation":h,...p,ref:b})})});t.displayName=n;var u="TabsList",v=e.forwardRef((a,b)=>{let{__scopeTabs:c,loop:e=!0,...f}=a,g=s(u,c),i=q(c);return(0,d.jsx)(h.bL,{asChild:!0,...i,orientation:g.orientation,dir:g.dir,loop:e,children:(0,d.jsx)(j.sG.div,{role:"tablist","aria-orientation":g.orientation,...f,ref:b})})});v.displayName=u;var w="TabsTrigger",x=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,disabled:g=!1,...i}=a,k=s(w,c),l=q(c),m=A(k.baseId,e),n=B(k.baseId,e),o=e===k.value;return(0,d.jsx)(h.q7,{asChild:!0,...l,focusable:!g,active:o,children:(0,d.jsx)(j.sG.button,{type:"button",role:"tab","aria-selected":o,"aria-controls":n,"data-state":o?"active":"inactive","data-disabled":g?"":void 0,disabled:g,id:m,...i,ref:b,onMouseDown:(0,f.m)(a.onMouseDown,a=>{g||0!==a.button||!1!==a.ctrlKey?a.preventDefault():k.onValueChange(e)}),onKeyDown:(0,f.m)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&k.onValueChange(e)}),onFocus:(0,f.m)(a.onFocus,()=>{let a="manual"!==k.activationMode;o||g||!a||k.onValueChange(e)})})})});x.displayName=w;var y="TabsContent",z=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:f,forceMount:g,children:h,...k}=a,l=s(y,c),m=A(l.baseId,f),n=B(l.baseId,f),o=f===l.value,p=e.useRef(o);return e.useEffect(()=>{let a=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,d.jsx)(i.C,{present:g||o,children:({present:c})=>(0,d.jsx)(j.sG.div,{"data-state":o?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":m,hidden:!c,id:n,tabIndex:0,...k,ref:b,style:{...a.style,animationDuration:p.current?"0s":void 0},children:c&&h})})});function A(a,b){return`${a}-trigger-${b}`}function B(a,b){return`${a}-content-${b}`}z.displayName=y;var C=c(96241);function D({className:a,...b}){return(0,d.jsx)(t,{"data-slot":"tabs",className:(0,C.cn)("flex flex-col gap-2",a),...b})}function E({className:a,...b}){return(0,d.jsx)(v,{"data-slot":"tabs-list",className:(0,C.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...b})}function F({className:a,...b}){return(0,d.jsx)(x,{"data-slot":"tabs-trigger",className:(0,C.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...b})}function G({className:a,...b}){return(0,d.jsx)(z,{"data-slot":"tabs-content",className:(0,C.cn)("flex-1 outline-none",a),...b})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},40918:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48971:(a,b,c)=>{Promise.resolve().then(c.bind(c,3582)),Promise.resolve().then(c.bind(c,55916))},55511:a=>{"use strict";a.exports=require("crypto")},55916:(a,b,c)=>{"use strict";c.d(b,{Tabs:()=>e,TabsContent:()=>h,TabsList:()=>f,TabsTrigger:()=>g});var d=c(61369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tabs.tsx","Tabs"),f=(0,d.registerClientReference)(function(){throw Error("Attempted to call TabsList() from the server but TabsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tabs.tsx","TabsList"),g=(0,d.registerClientReference)(function(){throw Error("Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tabs.tsx","TabsTrigger"),h=(0,d.registerClientReference)(function(){throw Error("Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\ui\\tabs.tsx","TabsContent")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65516:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92995:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>o,metadata:()=>m});var d=c(37413),e=c(94592),f=c(51358),g=c(99455),h=c(3582),i=c(55916),j=c(40918),k=c(17549),l=c(65516);let m={title:"Attendance | QRSAMS",description:"Monitor and track student attendance"},n=[{id:"STU001",name:"Juan Dela Cruz",grade:"Grade 12",section:"A",timeIn:"8:15 AM",timeOut:"-",status:"Present"},{id:"STU002",name:"Maria Santos",grade:"Grade 11",section:"B",timeIn:"8:12 AM",timeOut:"-",status:"Present"},{id:"STU003",name:"Pedro Garcia",grade:"Grade 12",section:"A",timeIn:"-",timeOut:"-",status:"Absent"}];function o(){return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Attendance"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Monitor and track student attendance records"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)(e.$,{variant:"outline",children:[(0,d.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Select Date"]}),(0,d.jsxs)(e.$,{children:[(0,d.jsx)(k.A,{className:"mr-2 h-4 w-4"}),"Open Scanner"]})]})]}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,d.jsxs)(f.Zp,{children:[(0,d.jsx)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Present Today"})}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-600",children:"1,180"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"95.6% of total students"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsx)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Absent Today"})}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-red-600",children:"54"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"4.4% of total students"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsx)(f.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:(0,d.jsx)(f.ZB,{className:"text-sm font-medium",children:"Late Arrivals"})}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:"23"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"1.9% of present students"})]})]})]}),(0,d.jsxs)(i.Tabs,{defaultValue:"today",className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)(i.TabsList,{children:[(0,d.jsx)(i.TabsTrigger,{value:"today",children:"Today"}),(0,d.jsx)(i.TabsTrigger,{value:"week",children:"This Week"}),(0,d.jsx)(i.TabsTrigger,{value:"month",children:"This Month"})]}),(0,d.jsxs)(e.$,{variant:"outline",children:[(0,d.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Export"]})]}),(0,d.jsx)(i.TabsContent,{value:"today",className:"space-y-4",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Today's Attendance"}),(0,d.jsx)(f.BT,{children:"January 8, 2024 - Real-time attendance tracking"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)(h.Table,{children:[(0,d.jsx)(h.TableHeader,{children:(0,d.jsxs)(h.TableRow,{children:[(0,d.jsx)(h.TableHead,{children:"Student ID"}),(0,d.jsx)(h.TableHead,{children:"Name"}),(0,d.jsx)(h.TableHead,{children:"Grade & Section"}),(0,d.jsx)(h.TableHead,{children:"Time In"}),(0,d.jsx)(h.TableHead,{children:"Time Out"}),(0,d.jsx)(h.TableHead,{children:"Status"})]})}),(0,d.jsx)(h.TableBody,{children:n.map(a=>(0,d.jsxs)(h.TableRow,{children:[(0,d.jsx)(h.TableCell,{className:"font-medium",children:a.id}),(0,d.jsx)(h.TableCell,{children:a.name}),(0,d.jsxs)(h.TableCell,{children:[a.grade," - ",a.section]}),(0,d.jsx)(h.TableCell,{children:a.timeIn}),(0,d.jsx)(h.TableCell,{children:a.timeOut}),(0,d.jsx)(h.TableCell,{children:(0,d.jsx)(g.E,{variant:"Present"===a.status?"secondary":"Absent"===a.status?"destructive":"outline",children:a.status})})]},a.id))})]})})]})}),(0,d.jsx)(i.TabsContent,{value:"week",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Weekly Attendance Summary"}),(0,d.jsx)(f.BT,{children:"Attendance overview for the current week"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)("p",{className:"text-muted-foreground",children:"Weekly attendance data will be displayed here."})})]})}),(0,d.jsx)(i.TabsContent,{value:"month",children:(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)(f.ZB,{children:"Monthly Attendance Summary"}),(0,d.jsx)(f.BT,{children:"Attendance overview for the current month"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)("p",{className:"text-muted-foreground",children:"Monthly attendance data will be displayed here."})})]})})]})]})}},94592:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(37413);c(61120);var e=c(70403),f=c(50662),g=c(66819);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},96752:(a,b,c)=>{"use strict";c.d(b,{Table:()=>f,TableBody:()=>h,TableCell:()=>k,TableHead:()=>j,TableHeader:()=>g,TableRow:()=>i});var d=c(60687);c(43210);var e=c(96241);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,d.jsx)("table",{"data-slot":"table",className:(0,e.cn)("w-full caption-bottom text-sm",a),...b})})}function g({className:a,...b}){return(0,d.jsx)("thead",{"data-slot":"table-header",className:(0,e.cn)("[&_tr]:border-b",a),...b})}function h({className:a,...b}){return(0,d.jsx)("tbody",{"data-slot":"table-body",className:(0,e.cn)("[&_tr:last-child]:border-0",a),...b})}function i({className:a,...b}){return(0,d.jsx)("tr",{"data-slot":"table-row",className:(0,e.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...b})}function j({className:a,...b}){return(0,d.jsx)("th",{"data-slot":"table-head",className:(0,e.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...b})}function k({className:a,...b}){return(0,d.jsx)("td",{"data-slot":"table-cell",className:(0,e.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...b})}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,410,190,39,69,609,144,483,676],()=>b(b.s=18112));module.exports=c})();