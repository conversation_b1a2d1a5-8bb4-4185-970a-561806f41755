exports.id=676,exports.ids=[676],exports.modules={2090:(a,b,c)=>{"use strict";c.d(b,{DashboardLayout:()=>X});var d=c(60687),e=c(43210),f=c(12941),g=c(7430),h=c(24934),i=c(21134),j=c(363),k=c(10218),l=c(57124),m=c(96241);function n({...a}){return(0,d.jsx)(l.bL,{"data-slot":"dropdown-menu",...a})}function o({...a}){return(0,d.jsx)(l.l9,{"data-slot":"dropdown-menu-trigger",...a})}function p({className:a,sideOffset:b=4,...c}){return(0,d.jsx)(l.ZL,{children:(0,d.jsx)(l.<PERSON>,{"data-slot":"dropdown-menu-content",sideOffset:b,className:(0,m.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",a),...c})})}function q({className:a,inset:b,variant:c="default",...e}){return(0,d.jsx)(l.q7,{"data-slot":"dropdown-menu-item","data-inset":b,"data-variant":c,className:(0,m.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...e})}function r({className:a,inset:b,...c}){return(0,d.jsx)(l.JU,{"data-slot":"dropdown-menu-label","data-inset":b,className:(0,m.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",a),...c})}function s({className:a,...b}){return(0,d.jsx)(l.wv,{"data-slot":"dropdown-menu-separator",className:(0,m.cn)("bg-border -mx-1 my-1 h-px",a),...b})}function t(){let{setTheme:a}=(0,k.D)();return(0,d.jsxs)(n,{children:[(0,d.jsx)(o,{asChild:!0,children:(0,d.jsxs)(h.$,{variant:"outline",size:"icon",children:[(0,d.jsx)(i.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,d.jsx)(j.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,d.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,d.jsxs)(p,{align:"end",children:[(0,d.jsx)(q,{onClick:()=>a("light"),children:"Light"}),(0,d.jsx)(q,{onClick:()=>a("dark"),children:"Dark"}),(0,d.jsx)(q,{onClick:()=>a("system"),children:"System"})]})]})}var u=c(58869),v=c(84027),w=c(40083),x=c(99208),y=c(11096);function z({className:a,...b}){return(0,d.jsx)(y.bL,{"data-slot":"avatar",className:(0,m.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",a),...b})}function A({className:a,...b}){return(0,d.jsx)(y.H4,{"data-slot":"avatar-fallback",className:(0,m.cn)("bg-muted flex size-full items-center justify-center rounded-full",a),...b})}function B(){let{data:a}=(0,x.wV)();if(!a?.user)return null;let b=a.user.name?.split(" ").map(a=>a[0]).join("").toUpperCase()||"U";return(0,d.jsxs)(n,{children:[(0,d.jsx)(o,{asChild:!0,children:(0,d.jsx)(h.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,d.jsx)(z,{className:"h-8 w-8",children:(0,d.jsx)(A,{children:b})})})}),(0,d.jsxs)(p,{className:"w-56",align:"end",forceMount:!0,children:[(0,d.jsx)(r,{className:"font-normal",children:(0,d.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium leading-none",children:a.user.name}),(0,d.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:a.user.email}),(0,d.jsx)("p",{className:"text-xs leading-none text-muted-foreground capitalize",children:a.user.role})]})}),(0,d.jsx)(s,{}),(0,d.jsxs)(q,{children:[(0,d.jsx)(u.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Profile"})]}),(0,d.jsxs)(q,{children:[(0,d.jsx)(v.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Settings"})]}),(0,d.jsx)(s,{}),(0,d.jsxs)(q,{onClick:()=>(0,x.CI)(),children:[(0,d.jsx)(w.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Log out"})]})]})]})}function C({onMenuClick:a}){return(0,d.jsx)("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,d.jsxs)("div",{className:"container flex h-16 items-center",children:[(0,d.jsxs)(h.$,{variant:"ghost",size:"icon",className:"md:hidden",onClick:a,children:[(0,d.jsx)(f.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"sr-only",children:"Toggle menu"})]}),(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"flex h-8 w-8 items-center justify-center rounded-lg bg-primary",children:(0,d.jsx)(g.A,{className:"h-5 w-5 text-primary-foreground"})}),(0,d.jsxs)("div",{className:"hidden sm:block",children:[(0,d.jsx)("h1",{className:"text-lg font-semibold",children:"QRSAMS"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Tanauan School of Arts and Trade"})]})]})}),(0,d.jsxs)("div",{className:"ml-auto flex items-center space-x-4",children:[(0,d.jsx)(t,{}),(0,d.jsx)(B,{})]})]})})}var D=c(85814),E=c.n(D),F=c(16189),G=c(32192),H=c(41312),I=c(90103),J=c(10022),K=c(53411),L=c(16103),M=c(24772);function N({className:a,children:b,...c}){return(0,d.jsxs)(M.bL,{"data-slot":"scroll-area",className:(0,m.cn)("relative",a),...c,children:[(0,d.jsx)(M.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:b}),(0,d.jsx)(O,{}),(0,d.jsx)(M.OK,{})]})}function O({className:a,orientation:b="vertical",...c}){return(0,d.jsx)(M.VM,{"data-slot":"scroll-area-scrollbar",orientation:b,className:(0,m.cn)("flex touch-none p-px transition-colors select-none","vertical"===b&&"h-full w-2.5 border-l border-l-transparent","horizontal"===b&&"h-2.5 flex-col border-t border-t-transparent",a),...c,children:(0,d.jsx)(M.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}let P=[{name:"Dashboard",href:"/dashboard",icon:G.A},{name:"Students",href:"/students",icon:H.A},{name:"Attendance",href:"/attendance",icon:I.A},{name:"Reports",href:"/reports",icon:J.A},{name:"Analytics",href:"/analytics",icon:K.A},{name:"Scanner",href:"/scanner",icon:L.A}];function Q({className:a}){let b=(0,F.usePathname)();return(0,d.jsx)("div",{className:(0,m.cn)("pb-12",a),children:(0,d.jsx)("div",{className:"space-y-4 py-4",children:(0,d.jsxs)("div",{className:"px-3 py-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,d.jsx)("div",{className:"flex h-8 w-8 items-center justify-center rounded-lg bg-primary",children:(0,d.jsx)(g.A,{className:"h-5 w-5 text-primary-foreground"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-lg font-semibold",children:"QRSAMS"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Tanauan School"})]})]}),(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("h2",{className:"mb-2 px-4 text-lg font-semibold tracking-tight",children:"Navigation"}),(0,d.jsx)(N,{className:"h-[300px] px-1",children:(0,d.jsx)("div",{className:"space-y-1",children:P.map(a=>(0,d.jsx)(h.$,{variant:b===a.href?"secondary":"ghost",className:"w-full justify-start",asChild:!0,children:(0,d.jsxs)(E(),{href:a.href,children:[(0,d.jsx)(a.icon,{className:"mr-2 h-4 w-4"}),a.name]})},a.href))})})]})]})})})}var R=c(26134),S=c(11860);function T({...a}){return(0,d.jsx)(R.bL,{"data-slot":"sheet",...a})}function U({...a}){return(0,d.jsx)(R.ZL,{"data-slot":"sheet-portal",...a})}function V({className:a,...b}){return(0,d.jsx)(R.hJ,{"data-slot":"sheet-overlay",className:(0,m.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function W({className:a,children:b,side:c="right",...e}){return(0,d.jsxs)(U,{children:[(0,d.jsx)(V,{}),(0,d.jsxs)(R.UC,{"data-slot":"sheet-content",className:(0,m.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===c&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===c&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===c&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===c&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",a),...e,children:[b,(0,d.jsxs)(R.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,d.jsx)(S.A,{className:"size-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function X({children:a}){let[b,c]=(0,e.useState)(!1);return(0,d.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,d.jsx)(C,{onMenuClick:()=>c(!0)}),(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)("aside",{className:"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 md:pt-16",children:(0,d.jsx)("div",{className:"flex-1 flex flex-col min-h-0 border-r bg-background",children:(0,d.jsx)(Q,{})})}),(0,d.jsx)(T,{open:b,onOpenChange:c,children:(0,d.jsx)(W,{side:"left",className:"p-0 w-64",children:(0,d.jsx)(Q,{})})}),(0,d.jsx)("main",{className:"flex-1 md:ml-64",children:(0,d.jsx)("div",{className:"p-6",children:a})})]})]})}},6753:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>f});var d=c(60687);c(43210);var e=c(10218);function f({children:a,...b}){return(0,d.jsx)(e.N,{...b,children:a})}},8875:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},9763:(a,b,c)=>{"use strict";c.d(b,{DashboardLayout:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\dashboard-layout.tsx","DashboardLayout")},11888:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>f});var d=c(60687),e=c(99208);function f({children:a}){return(0,d.jsx)(e.CP,{children:a})}},12071:(a,b,c)=>{Promise.resolve().then(c.bind(c,6753)),Promise.resolve().then(c.bind(c,11888))},14042:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\providers\\session-provider.tsx","AuthProvider")},14451:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},24934:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(96241);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},46055:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},51358:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(37413);c(61120);var e=c(66819);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},52239:(a,b,c)=>{Promise.resolve().then(c.bind(c,81707)),Promise.resolve().then(c.bind(c,14042))},58014:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l,metadata:()=>k});var d=c(37413),e=c(260),f=c.n(e),g=c(73298),h=c.n(g);c(82704);var i=c(81707),j=c(14042);let k={title:"QRSAMS - Tanauan School of Arts and Trade",description:"QR-Code Based Student Attendance and Monitoring System"};function l({children:a}){return(0,d.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:(0,d.jsx)(i.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,d.jsx)(j.AuthProvider,{children:a})})})})}},60758:(a,b,c)=>{"use strict";c.d(b,{j2:()=>i,Y9:()=>h});var d=c(19443),e=c(10189);let f=[{id:"1",email:"<EMAIL>",password:"admin123",name:"Administrator",role:"admin"},{id:"2",email:"<EMAIL>",password:"teacher123",name:"Teacher",role:"teacher"}],g={pages:{signIn:"/login"},providers:[(0,e.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(a){if(!a?.email||!a?.password)return null;let b=f.find(b=>b.email===a.email);return b&&b.password===a.password?{id:b.id,email:b.email,name:b.name,role:b.role}:null}})],callbacks:{jwt:async({token:a,user:b})=>(b&&(a.role=b.role),a),session:async({session:a,token:b})=>(b&&(a.user.id=b.sub,a.user.role=b.role),a)},session:{strategy:"jwt"}},{handlers:h,auth:i,signIn:j,signOut:k}=(0,d.Ay)(g)},66819:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}},68907:(a,b,c)=>{Promise.resolve().then(c.bind(c,2090))},71934:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h});var d=c(37413),e=c(39916),f=c(60758),g=c(9763);async function h({children:a}){return await (0,f.j2)()||(0,e.redirect)("/login"),(0,d.jsx)(g.DashboardLayout,{children:a})}},74483:(a,b,c)=>{Promise.resolve().then(c.bind(c,9763))},81707:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\qrsams\\components\\layout\\theme-provider.tsx","ThemeProvider")},82704:()=>{},96241:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},99455:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(37413);c(61120);var e=c(70403),f=c(50662),g=c(66819);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}}};